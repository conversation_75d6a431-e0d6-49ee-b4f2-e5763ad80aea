package com.taobao.mc.aimi.util

import com.intellij.openapi.application.backgroundWriteAction
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.command.CommandProcessor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.openapi.vfs.VfsUtilCore
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.vfs.isFile
import com.intellij.openapi.vfs.readText
import com.intellij.util.application
import com.taobao.mc.aimi.ext.FileStats
import com.taobao.mc.aimi.ext.FileType
import com.taobao.mc.aimi.ext.core.UriUtils
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.CompletableDeferred
import java.io.IOException
import kotlin.math.min

class FileUtils(private val project: Project) {
    private val logger = LoggerManager.getLogger(FileUtils::class.java)
    // todo: use VFS (it's moved from IntellijIde)

    fun fileExists(fileUri: String): Boolean =
        findFile(fileUri) != null

    /**
     * 写入文件内容，支持撤回操作
     * @param uri 文件URI
     * @param contents 文件内容
     * @param project 项目实例，用于支持撤回操作。如果为null，将尝试获取当前打开的项目
     */
    fun writeFile(uri: String, contents: String, project: Project? = null) {
        application.runWriteAction {
            val virtualFile = UriUtils.uriToVirtualFile(uri) ?: run {
                // 如果虚拟文件不存在，先创建物理文件
                val file = UriUtils.uriToFile(uri)
                file.parentFile?.mkdirs()
                file.writeText(contents)
                // 刷新 VFS 以让 IntelliJ 感知新文件
                VfsUtil.findFileByIoFile(file, true)
            }

            virtualFile?.let { vFile ->
                val document = FileDocumentManager.getInstance().getDocument(vFile)
                if (document != null) {
                    // 获取项目实例，优先使用传入的project参数
                    val targetProject = project ?: ProjectManager.getInstance().openProjects.firstOrNull()

                    // 使用 CommandProcessor 包装文档修改操作，确保支持撤回
                    CommandProcessor.getInstance().executeCommand(targetProject, {
                        document.setText(contents)
                    }, "AIMI File Write", "AIMI")

                    FileDocumentManager.getInstance().saveDocument(document)
                } else {
                    // 直接修改虚拟文件内容（不支持撤回）
                    vFile.setBinaryContent(contents.toByteArray(vFile.charset))
                }
            }
        }
    }

    fun writeFile(fileUri: String, content: String) {
        val path = VfsUtilCore.urlToPath(fileUri)
        val pathDirectory = VfsUtil.getParentDir(path)
            ?: return
        val vfsDirectory = VfsUtil.createDirectories(pathDirectory)
        val pathFilename = VfsUtil.extractFileName(path)
            ?: return
        runWriteAction {
            val newFile = vfsDirectory.createChildData(this, pathFilename)
            VfsUtil.saveText(newFile, content)
        }
    }

    fun listDir(fileUri: String): List<List<Any>> {
        // val isValidDir = continuePluginService.workspacePaths?.any { dir.startsWith(it) } ?: false
        // if (!isValidDir) {
        //     logger.warn("listDir---isInValidDir---$dir")
        // }
        /*val files = UriUtils.uriToFileSafe(dir)?.listFiles()?.map {
            listOf(it.name, if (it.isDirectory) FileType.DIRECTORY.value else FileType.FILE.value)
        } ?: emptyList()

        return files*/
        val found = findFile(fileUri)
            ?: return emptyList()
        if (!found.isDirectory)
            return emptyList()
        return found.children.map { file ->
            val fileType = if (file.isDirectory)
                FileType.DIRECTORY.value
            else
                FileType.FILE.value
            listOf(file.name, fileType)
        }
    }

    fun readFile(uri: String): String {
        return try {
            val virtualFile = UriUtils.uriToVirtualFile(uri)
            val isModifiedFile = virtualFile?.let {
                it.isFile && FileDocumentManager.getInstance().isFileModified(it)
            } ?: false
            val content = if (isModifiedFile) {
                runReadAction {
                    FileDocumentManager.getInstance().getDocument(virtualFile)?.text
                }
            } else null
            content ?: virtualFile?.readText() ?: run {
                val file = UriUtils.uriToFile(uri)
                if (!file.exists() || file.isDirectory) return ""
                file.bufferedReader().readText()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }

    fun readFile(fileUri: String, maxLength: Int = 100_000): String {
        val found = findFile(fileUri)
            ?: return ""
        val text = runReadAction {
            // note: document (if exists) is more up-to-date than VFS
            readDocument(found, maxLength) ?: VfsUtil.loadText(found, maxLength)
        }
        return normalizeLineEndings(text)
    }

    fun openFile(fileUri: String) {
        val found = findFile(fileUri)
            ?: return
        FileEditorManager.getInstance(project).openFile(found, true)
    }

    fun saveFile(fileUri: String) {
        val found = findFile(fileUri)
            ?: return
        val manager = FileDocumentManager.getInstance()
        val document = manager.getDocument(found)
            ?: return
        manager.saveDocument(document)
    }

    fun getFileStats(fileUris: List<String>): Map<String, FileStats> =
        fileUris.mapNotNull { fileUri ->
            val file = findFile(fileUri)
                ?: return@mapNotNull null
            fileUri to FileStats(file.timeStamp, file.length)
        }.toMap()

    suspend fun deleteFile(project: Project, path: String): Boolean {
        val deleteDeferred = CompletableDeferred<Boolean>()
        backgroundWriteAction {
            val virtualFile = UriUtils.uriToVirtualFile(path)
            virtualFile?.let { vFile ->
                // 使用 CommandProcessor 包装删除操作，确保支持撤回
                CommandProcessor.getInstance().executeCommand(project, {
                    try {
                        vFile.delete(this)
                        deleteDeferred.complete(true)
                    } catch (e: IOException) {
                        logger.warn("Failed to delete file: $path", e)
                        deleteDeferred.complete(false)
                    }
                }, "AIMI File Delete", "AIMI")
            }
        }
        return deleteDeferred.await()
    }


    private fun findFile(fileUri: String): VirtualFile? {
        val noParams = fileUri.substringBefore("?")
        val normalizedAuthority = normalizeWindowsAuthority(noParams)
        return VirtualFileManager.getInstance()
            .refreshAndFindFileByUrl(normalizedAuthority)
    }

    private fun readDocument(file: VirtualFile, maxLength: Int): String? {
        val document = FileDocumentManager.getInstance().getDocument(file)
            ?: return ""
        val length = min(document.textLength, maxLength)
        return document.getText(TextRange(0, length))
    }

    private fun normalizeLineEndings(text: String) =
        text.replace("\r\n", "\n")
            .replace("\r", "\n")

    private fun normalizeWindowsAuthority(fileUri: String): String {
        val authorityPrefix = "file://"
        val noAuthorityPrefix = "file:///"
        if (fileUri.startsWith(authorityPrefix) && !fileUri.startsWith(noAuthorityPrefix)) {
            val path = fileUri.substringAfter(authorityPrefix)
            return "$noAuthorityPrefix$path"
        }
        return fileUri
    }
}
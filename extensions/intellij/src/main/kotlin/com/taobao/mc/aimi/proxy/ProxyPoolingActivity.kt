package com.taobao.mc.aimi.proxy

import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.*
import kotlin.time.Duration.Companion.seconds

class ProxyPoolingActivity : ProjectActivity {
    private val scope = CoroutineScope(Dispatchers.Default)
    private var lastSettings = ProxySettings.getSettings()
    private val logger = LoggerManager.getLogger(javaClass)

    override suspend fun execute(project: Project) {
        scope.launch {
            while (isActive) {
                val newSettings = ProxySettings.getSettings()
                if (newSettings != lastSettings) {
                    onSettingsChanged(project)
                    lastSettings = newSettings
                }
                delay(2.seconds)
            }
        }
    }

    private fun onSettingsChanged(project: Project) {
        logger.warn("Proxy settings changed, restarting")
        project.service<AIMIPluginService>().coreMessenger?.restart()
    }
}




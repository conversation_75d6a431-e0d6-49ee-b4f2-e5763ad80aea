package com.taobao.mc.aimi.editor

import com.intellij.openapi.Disposable
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.event.CaretEvent
import com.intellij.openapi.editor.event.CaretListener
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.utils.Debouncer
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.listeners.IActiveEditorChangeListener
import com.taobao.mc.aimi.track.TelemetryEventTrack
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import java.lang.ref.WeakReference

class AIMICaretListener(private val project: Project) : CaretListener, IActiveEditorChangeListener, Disposable {
    private var latestEditorPath: String? = null
    private var latestEditor = WeakReference<Editor>(null)
    private val scope = CoroutineScope(Dispatchers.IO)
    private val debouncer = Debouncer(500, scope)

    override fun caretPositionChanged(event: CaretEvent) {
        val editor = event.editor
        editor.virtualFile ?: return
        if (!editor.virtualFile.isWritable) return
        debouncer.debounce {
            val newPosition = event.newPosition
            val oldPosition = event.oldPosition
            if (newPosition == oldPosition && latestEditor == editor) return@debounce

            editor.virtualFile.toUriOrNull()
            val filepath = editor.virtualFile.path

            TelemetryEventTrack.trackCursorChange(
                project = project,
                filepath = filepath,
                newPosition = newPosition,
                oldFilepath = latestEditorPath,
                oldPosition = oldPosition
            )

            latestEditorPath = filepath
            latestEditor = WeakReference(editor)
        }
    }

    override fun notifyEditorChanged(editor: Editor?) {
        editor?.virtualFile ?: return
        if (!editor.virtualFile.isWritable) return

        debouncer.debounce {
            val filepath = editor.virtualFile.path
            val newPosition = editor.caretModel.logicalPosition

            val oldEditor = latestEditor.get()
            val oldFilepath = latestEditorPath
            val oldPosition = oldEditor?.caretModel?.logicalPosition

            if (newPosition == oldPosition && latestEditor.get() == editor) return@debounce

            TelemetryEventTrack.trackCursorChange(
                project = project,
                filepath = filepath,
                newPosition = newPosition,
                oldFilepath = oldFilepath,
                oldPosition = oldPosition
            )

            latestEditorPath = filepath
            latestEditor = WeakReference(editor)
        }
    }

    override fun dispose() {
        latestEditorPath = null
        latestEditor.clear()
        scope.cancel()
    }
}
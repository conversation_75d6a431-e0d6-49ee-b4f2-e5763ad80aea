package com.taobao.mc.aimi.types

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable
data class ChangeWindowParams(
    @Serializable(with = WindowTypeSerializer::class) val type: WindowType, val url: String?
)

// chat history integration
@Serializable
sealed class WindowType(val type: String) {
    data object Chat : WindowType("chat")
    data object History : WindowType("历史会话")
    data object Integration : WindowType("MC持续集成迭代")
    data object API : WindowType("api")
    data object Continue : WindowType("continue")
}

object WindowTypeSerializer : KSerializer<WindowType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("WindowType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: WindowType) {
        encoder.encodeString(value.type.lowercase())
    }

    override fun deserialize(decoder: Decoder): WindowType {
        val typeString = decoder.decodeString().lowercase()
        return when (typeString) {
            "chat" -> WindowType.Chat
            "history" -> WindowType.History
            "integration" -> WindowType.Integration
            "api" -> WindowType.API
            "continue" -> WindowType.Continue
            else -> throw IllegalArgumentException("Unknown WindowType: $typeString")
        }
    }
}

@Serializable
data class ShowProgressParams(
    val id: String,
    val title: String,
    val progress: Double = 0.0,
    val status: String = "start",
    @Serializable(with = ProgressTypeSerializer::class)
    val type: ProgressType = ProgressType.Apply,
)

@Serializable
sealed class ProgressType(val type: String) {
    data object Apply : ProgressType("apply")
    data object Chat : ProgressType("chat")
}

object ProgressTypeSerializer : KSerializer<ProgressType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("ProgressType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: ProgressType) {
        encoder.encodeString(value.type.lowercase())
    }

    override fun deserialize(decoder: Decoder): ProgressType {
        val typeString = decoder.decodeString().lowercase()
        return when (typeString) {
            "apply" -> ProgressType.Apply
            "chat" -> ProgressType.Chat
            else -> throw IllegalArgumentException("Unknown ProgressType: $typeString")
        }
    }
}

@Serializable
data class GetProblemsParams(
    val filepath: String
)

/**
 * 用户信息数据类，对应前端的 UserVO 接口
 */
@Serializable
data class UserInfo(
    /** 工号 */
    val empId: String? = null,
    /** 花名 */
    val nickName: String? = null,
    /** 真名 */
    val name: String? = null,
    /** 英文名 */
    val englishName: String? = null,
    /** 邮箱 */
    val email: String? = null,
    /** 职位描述：eg.技术-基础平台-开发 */
    val jobName: String? = null,
    /** 员工类型：正式\外包 */
    val empType: String? = null,
    /** 离职状态 A：在职 I：离职 */
    val workStatus: String? = null,
    /** 主管工号 */
    val superWorkNo: String? = null,
    /** 公司编号 */
    val corpDeptNo: String? = null,
    /** 公司名称，淘天集团 */
    val corpName: String? = null,
    /** 公司英文名 */
    val corpEnName: String? = null,
    /** BG编号 */
    val bgDeptNo: String? = null,
    /** BG名称，淘天集团-业务技术 */
    val bgName: String? = null,
    /** BG英文名 */
    val bgEnName: String? = null,
    /** BU编号 */
    val buDeptNo: String? = null,
    /** BU名称，淘天集团-业务技术-终端平台 */
    val buName: String? = null,
    /** BU名称，淘天集团-业务技术-终端平台 */
    val bu: String? = null,
    /** BU英文名 */
    val buEnName: String? = null,
    /** 部门编号 */
    val deptNo: String? = null,
    /** 部门名称 */
    val deptName: String? = null,
    /** 部门英文名称 */
    val deptEnName: String? = null,
    /** 钉钉Id */
    val dingTalkId: String? = null,
    /** 钉钉Nick */
    val dingTalkNick: String? = null,
    /** 钉钉地址 */
    val dingTalkUrl: String? = null,
    /** 头像 */
    val avatar: String? = null,
    /** 内外地址 */
    val workUrl: String? = null
)

@Serializable
data class SetLoginInfoParams(
    val user: UserInfo
)

enum class ShortcutType(val type: String) {
    ToggleChat("toggleChat"),
}

@Serializable
data class GetShortcutsParams(
    @Serializable(ShortCutTypeSerializer::class)
    val type: ShortcutType
)

private object ShortCutTypeSerializer : KSerializer<ShortcutType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("ShortcutType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: ShortcutType) {
        encoder.encodeString(value.type)
    }

    override fun deserialize(decoder: Decoder): ShortcutType {
        val string = decoder.decodeString()
        return ShortcutType.entries.firstOrNull { it.type == string } ?: throw IllegalArgumentException("Unknown ShortcutType: $string")
    }
}
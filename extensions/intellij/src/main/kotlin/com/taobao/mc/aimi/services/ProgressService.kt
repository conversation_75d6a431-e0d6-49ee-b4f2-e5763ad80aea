package com.taobao.mc.aimi.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.project.Project
import java.util.concurrent.ConcurrentHashMap

/**
 * 剪切板管理服务
 * 实现对全局剪切板的监听，维护最后的剪切板信息
 * 当内容来自当前IDE时，同时记录文件路径、行号等详细信息
 */
@Service(Service.Level.PROJECT)
class ProgressService(private val project: Project) {
    companion object {
        fun getInstance(project: Project): ProgressService = project.service()
    }

    private val progressIndicators = ConcurrentHashMap<String, ProgressIndicator>()

    /**
     * 添加或更新进度指示器
     * @param id 进度指示器的唯一标识
     * @param indicator 进度指示器实例
     */
    fun addProgressIndicator(id: String, indicator: ProgressIndicator): ProgressIndicator {
        progressIndicators[id] = indicator
        return indicator
    }

    /**
     * 获取指定ID的进度指示器
     * @param id 进度指示器的唯一标识
     * @return 进度指示器实例，如果不存在则返回null
     */
    fun getProgressIndicator(id: String): ProgressIndicator? {
        return progressIndicators[id]
    }

    /**
     * 移除指定ID的进度指示器
     * @param id 进度指示器的唯一标识
     * @return 被移除的进度指示器，如果不存在则返回null
     */
    fun removeProgressIndicator(id: String): ProgressIndicator? {
        return progressIndicators.remove(id)
    }

    /**
     * 取消并移除指定ID的进度指示器
     * @param id 进度指示器的唯一标识
     */
    fun cancelAndRemoveProgressIndicator(id: String) {
        progressIndicators[id]?.let { indicator ->
            if (!indicator.isCanceled) {
                indicator.cancel()
            }
            progressIndicators.remove(id)
        }
    }

    /**
     * 检查指定ID的进度指示器是否存在
     * @param id 进度指示器的唯一标识
     * @return 如果存在返回true，否则返回false
     */
    fun hasProgressIndicator(id: String): Boolean {
        return progressIndicators.containsKey(id)
    }

    /**
     * 获取所有进度指示器的ID列表
     * @return ID列表
     */
    fun getAllProgressIds(): Set<String> {
        return progressIndicators.keys.toSet()
    }

    /**
     * 清除所有进度指示器
     */
    fun clearAllProgressIndicators() {
        progressIndicators.values.forEach { indicator ->
            if (!indicator.isCanceled) {
                indicator.cancel()
            }
        }
        progressIndicators.clear()
    }
}
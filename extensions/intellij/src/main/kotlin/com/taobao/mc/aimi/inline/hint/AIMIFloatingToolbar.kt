package com.taobao.mc.aimi.inline.hint

import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.actionSystem.impl.ActionButton
import com.intellij.openapi.actionSystem.impl.ActionButtonWithText
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.keymap.KeymapManager
import com.intellij.openapi.keymap.KeymapUtil
import com.intellij.ui.LightweightHint
import com.intellij.util.ui.JBUI
import com.taobao.mc.aimi.inline.utils.PanelUtils
import com.taobao.mc.aimi.util.AIMIIcons
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.FlowLayout
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import javax.swing.BorderFactory
import javax.swing.JComponent
import javax.swing.JLabel
import javax.swing.JPanel

/**
 * com.aone.inline.hint.CopilotFloatingToolbar 的 Kotlin 翻译
 */
object AIMIFloatingToolbar {

    private val DEFAULT_MINIMUM_BUTTON_SIZE: Dimension = JBUI.size(30, 20)

    /**
     * 创建一个包含 "添加到聊天"、"快速编辑" 和一个下拉菜单的内联聊天提示（Hint）。
     */
    @JvmStatic
    fun createInlineChatHint(editor: Editor): LightweightHint {
        val chatButton = PanelUtils.createTextButton("aimi.focusInputWithoutClear", "添加到聊天")
        val diffButton = PanelUtils.createTextButton("aimi.focusInput", "优化代码")
        val dropdownButton = createDropdownButton(editor)

        val hint = createHint(listOf(chatButton, diffButton /*dropdownButton*/), editor)

        addButtonClickHandler(chatButton, editor, hint)
        addButtonClickHandler(diffButton, editor, hint)
        return hint
    }

    /**
     * 根据给定的操作按钮列表创建一个 LightweightHint。
     */
    @JvmStatic
    internal fun createHint(actionButtons: List<JComponent>, editor: Editor): LightweightHint {
        val buttonPanel = JPanel(FlowLayout(FlowLayout.LEFT, 6, 0)).apply {
            isOpaque = false
        }

        val userIcon = JLabel(AIMIIcons.AIMI).apply {
            border = BorderFactory.createEmptyBorder(1, 0, 0, 0)
        }

        buttonPanel.add(userIcon, BorderLayout.NORTH)

        actionButtons.forEach { button ->
            buttonPanel.add(button, BorderLayout.CENTER)
        }

        val component = JPanel(BorderLayout()).apply {
            isOpaque = false
            border = JBUI.Borders.empty(5)
            add(buttonPanel, BorderLayout.CENTER)
        }

        return LightweightHint(component).apply {
            setCancelOnClickOutside(false)
            setForceShowAsPopup(true)
        }
    }

    /**
     * 创建一个带图标的动作按钮。
     */
    @JvmStatic
    internal fun createIconButton(
        action: AnAction,
        place: String,
        presentation: Presentation,
        minimumSize: () -> Dimension
    ): ActionButton {
        return ActionButton(action, presentation, place, minimumSize())
    }

    /**
     * 为一个按钮添加点击处理器，该处理器会执行按钮的动作并隐藏提示（Hint）。
     */
    private fun addButtonClickHandler(button: ActionButtonWithText, editor: Editor, hint: LightweightHint) {
        button.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                hint.hide()
                val action = button.action
                val dataContext = createSimpleDataContext(editor)
                val event = AnActionEvent.createEvent(
                    action,
                    dataContext,
                    null,
                    "EditorToolbar",
                    ActionUiKind.NONE,
                    null,
                )
                action.actionPerformed(event)
            }
        })
    }

    /**
     * 创建一个下拉菜单按钮。
     */
    private fun createDropdownButton(editor: Editor): JComponent {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction("aimi.EditorPopupMenu")
        val presentation = action.templatePresentation.clone()
        val buttonPresentation = Presentation(presentation.text).apply {
            copyFrom(presentation)
        }

        val dropdownButton = ActionButtonWithText(
            action,
            buttonPresentation,
            "EditorToolbar",
            DEFAULT_MINIMUM_BUTTON_SIZE
        )

        dropdownButton.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent) {
                showDropdownMenu(dropdownButton, editor)
            }
        })
        return dropdownButton
    }

    /**
     * 显示下拉菜单。
     */
    private fun showDropdownMenu(component: JComponent, editor: Editor) {
        // val json = AoneCopilotSettings.getSettingsState().inlineChatQuickActionContexts
        // if (json.isEmpty()) {
        //     return
        // }
        //
        // val entries = InlineChatQuickActionContext.parse(json)
        // val actions = mutableListOf<AnAction>()
        //
        // for (entry in entries) {
        //     var actionName = entry.name
        //     if (BooleanUtils.isTrue(entry.isCodeModification)) {
        //         actionName = String.format("%s %s", actionName, "*")
        //     }
        //
        //     // `name` 变量被lambda捕获
        //     val name = actionName
        //     val action = CommonQuickInlineAction(
        //         editor,
        //         { name }, // lambda$showDropdownMenu$0
        //         entry.isCodeModification,
        //         entry.command
        //     )
        //     actions.add(action)
        // }
        //
        // val addMore = object : AnAction(
        //     { CopilotBundle.message("aone.copilot.inline.chat.quick.action.add.more") }, // lambda$showDropdownMenu$1
        //     AllIcons.Actions.InlayGear
        // ) {
        //     override fun actionPerformed(e: AnActionEvent) {
        //         ShowSettingsUtil.getInstance().showSettingsDialog(
        //             editor.project,
        //             AoneCopilotConfigurable::class.java
        //         )
        //     }
        // }
        // actions.add(addMore)
        //
        // val actionGroup = DefaultActionGroup(actions)
        // val popupMenu = ActionManager.getInstance()
        //     .createActionPopupMenu("CopilotDropdown", actionGroup)
        //
        // popupMenu.component.show(component, 0, component.size.height + 10)
    }

    /**
     * 获取一个动作的快捷键文本。
     */
    private fun getShortcutText(action: AnAction): String {
        val keymapManager = KeymapManager.getInstance()
        val keymap = keymapManager.activeKeymap
        val actionId = ActionManager.getInstance().getId(action)
        val shortcuts = keymap.getShortcuts(actionId)

        val firstShortcut = shortcuts.firstOrNull()
        if (firstShortcut is KeyboardShortcut) {
            return KeymapUtil.getShortcutText(firstShortcut)
        }

        return ""
    }

    private fun createSimpleDataContext(editor: Editor): DataContext {
        return SimpleDataContext.builder()
            .add(CommonDataKeys.EDITOR, editor)
            .add(CommonDataKeys.PROJECT, editor.project)
            .build()
    }
}

package com.taobao.mc.aimi.psi.types

/**
 * 对象种类
 */
enum class ObjectKind {
    THIS,           // this对象
    SUPER,          // super对象
    LOCAL_VARIABLE, // 局部变量
    PARAMETER,      // 方法参数
    FIELD,          // 字段
    STATIC_FIELD,   // 静态字段
    METHOD_CALL,    // 方法调用结果
    EXPRESSION,     // 表达式结果
    LAMBDA_PARAMETER, // Lambda参数
    LAMBDA_RECEIVER,  // Lambda接收者
    DOT_CONTEXT,       // .号调用
}
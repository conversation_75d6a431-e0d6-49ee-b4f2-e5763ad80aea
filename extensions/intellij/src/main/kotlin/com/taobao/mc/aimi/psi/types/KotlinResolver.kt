package com.taobao.mc.aimi.psi.types

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.diagnostic.Logger
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiWhiteSpace
import com.intellij.psi.util.parentOfType
import com.intellij.psi.util.startOffset
import org.jetbrains.kotlin.idea.testIntegration.framework.KotlinPsiBasedTestFramework.Companion.asKtNamedFunction
import org.jetbrains.kotlin.psi.KtArrayAccessExpression
import org.jetbrains.kotlin.psi.KtCallExpression
import org.jetbrains.kotlin.psi.KtContainerNode
import org.jetbrains.kotlin.psi.KtDotQualifiedExpression
import org.jetbrains.kotlin.psi.KtExpression
import org.jetbrains.kotlin.psi.KtFunctionLiteral
import org.jetbrains.kotlin.psi.KtLambdaExpression
import org.jetbrains.kotlin.psi.KtSafeQualifiedExpression
import org.jetbrains.kotlin.psi.psiUtil.referenceExpression

interface KotlinResolver : IObjectResolver {
    val resolverModel: String
    val logger: Logger

    /**
     * 解析 Kotlin 对象
     */
    override fun resolveObjects(psiFile: PsiFile, element: PsiElement): List<ObjectInfo> {
        // 使用协程在后台线程中执行Analysis API，避免在EDT线程中执行
        return ReadAction.compute<List<ObjectInfo>, RuntimeException> {
            val objects = mutableListOf<ObjectInfo>()

            // 首先检查是否在方法调用的括号内，如果是，找到方法调用的接收者
            val findMethodCallReceiver = findMethodCallReceiver(element)
            if (findMethodCallReceiver != null) {
                val methodCall = element.parentOfType<KtDotQualifiedExpression>() ?: element.parentOfType<KtSafeQualifiedExpression>()
                if (methodCall != null) {
                    val methodCallReceiver = methodCall.receiverExpression
                    val methodExpression = methodCall.selectorExpression
                    val methodName = methodExpression?.asKtNamedFunction()?.name
                        ?: methodExpression?.referenceExpression()?.text
                        ?: methodExpression?.text?.substringBeforeLast("(")
                    logger.debug("Found method call receiver: ${methodCallReceiver.text}, method name: $methodName")
                    val receiverInfo = resolveExpressionType(methodCallReceiver)?.apply {
                        if (!methodName.isNullOrEmpty()) {
                            members = members.filter { it.name.contains(methodName, true) }
                        }
                    }
                    if (receiverInfo != null) {
                        objects.add(receiverInfo.copy(confidence = 1.0, kind = ObjectKind.METHOD_CALL)) // 最高优先级
                    }
                }
            }

            // 如果在方法调用的接收者找到了, 后面的低优先级就不找了
            // if (objects.isNotEmpty()) return@compute objects

            // 检查是否在点号后面
            val dotContext = findKotlinDotContext(psiFile, element)
            if (dotContext != null) {
                val identifier = dotContext.getCleanIdentifier()
                val objectInfo = resolveExpressionType(dotContext.expression)?.apply {
                    if (identifier.isNotEmpty() && dotContext.expression.parent !is KtArrayAccessExpression) {
                        members = members.filter { it.name.contains(identifier, true) }
                    }
                }
                if (objectInfo != null) {
                    objects.add(objectInfo.copy(confidence = 1.0, kind = ObjectKind.DOT_CONTEXT))
                }
            } else {
                objects.addAll(findAvailableKotlinObjects(element))
            }

            objects.sortedByDescending { it.confidence }
        }
    }

    /**
     * 查找 Kotlin 点号上下文
     */
    fun findKotlinDotContext(psiFile: PsiFile, element: PsiElement): DotRecord<KtExpression>? {
        val parent = (element.parent as? KtDotQualifiedExpression ?: element.parent.parent as? KtDotQualifiedExpression) ?: run {
            (element.parent as? KtSafeQualifiedExpression ?: element.parent.parent as? KtSafeQualifiedExpression)
        }
        if (parent != null) {
            val ktExpression = parent.receiverExpression
            return DotRecord(element.text, ktExpression)
        }
        if (element is PsiWhiteSpace) {
            psiFile.findElementAt(element.startOffset - 1)?.let { element ->
                if (element is KtDotQualifiedExpression || element is KtSafeQualifiedExpression) {
                    val ktExpression = element.receiverExpression
                    return DotRecord(element.text, ktExpression)
                }
                return findKotlinDotContext(psiFile, element)
            }
        } else if (element.parent is KtContainerNode) {
            (element.parent.parent as? KtArrayAccessExpression)?.arrayExpression?.let {
                return DotRecord(it.text, it)
            }
        }
        return null
    }

    fun resolveExpressionType(expression: KtExpression): ObjectInfo?

    fun findAvailableKotlinObjects(element: PsiElement): List<ObjectInfo>

    /**
     * 查找方法调用的接收者对象
     */
    private fun findMethodCallReceiver(element: PsiElement): PsiElement? {
        var current: PsiElement? = element

        // 向上查找，寻找方法调用表达式
        while (current != null) {
            when (current) {
                // Kotlin方法调用
                is KtCallExpression -> {
                    val parent = current.parent
                    if (parent is KtDotQualifiedExpression) {
                        return parent.receiverExpression
                    }
                }
                // Kotlin安全调用
                is KtSafeQualifiedExpression -> {
                    return current.receiverExpression
                }

                is KtFunctionLiteral, is KtLambdaExpression -> return null
            }
            current = current.parent
        }

        return null
    }
}
package com.taobao.mc.aimi.psi.types

import com.intellij.psi.PsiElement
import com.taobao.mc.aimi.types.SymbolInfo
import org.jetbrains.kotlin.util.removeSuffixIfPresent

/**
 * 对象信息
 */
data class ObjectInfo constructor(
    val name: String,                    // 对象名称（如：this, super, 变量名等）
    val type: String,                    // 类型名称
    val qualifiedType: String?,          // 完全限定类型名
    val kind: ObjectKind,                // 对象种类
    val element: PsiElement?,            // 对应的PSI元素
    var members: List<MemberInfo>,       // 可访问的成员
    val isNullable: Boolean = false,     // 是否可为null（主要用于Kotlin）
    val confidence: Double = 1.0,        // 匹配置信度
    val filepath: String? = null,        // 源码/文件路径
    val isLambdaParameter: Boolean = false, // 是否为Lambda参数
    val lambdaContext: String? = null,   // Lambda上下文信息,
    val content: String? = null,           // 类的内容, 给特殊的类准备的, 比如 data class/ enum class / sealed class 等
) {
    fun toSymbolInfo(): SymbolInfo {
        val members = if (kind == ObjectKind.THIS || kind == ObjectKind.SUPER) {
            members
        } else {
            members.filter { it.isAccessible() }
        }

        val memberContent = members.joinToString("\n") { member ->
            val prefix = member.visibility
            when (member.kind) {
                MemberKind.FIELD -> {
                    "$prefix ${member.name}: ${member.type}"
                }

                MemberKind.PROPERTY -> {
                    "$prefix ${member.name}: ${member.type}"
                }

                MemberKind.METHOD -> {
                    "$prefix ${member.signature}"
                }

                MemberKind.CONSTRUCTOR -> {
                    "$prefix ${member.signature}"
                }
            }
        }

        return SymbolInfo(
            filepath = (qualifiedType ?: type).removeSuffixIfPresent("?"),
            content = memberContent + (this.content ?: "")
        )
    }
}
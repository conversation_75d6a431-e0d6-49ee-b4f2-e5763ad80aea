package com.taobao.mc.aimi.execution

import com.intellij.execution.ExecutionManager
import com.intellij.execution.executors.DefaultRunExecutor
import com.intellij.execution.process.ProcessHandler
import com.intellij.execution.runners.ExecutionEnvironmentBuilder
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.taobao.mc.aimi.ext.protocol.TerminalOptions
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.CompletableDeferred
import java.io.File


// private val TerminalID = AtomicLong(0)
val logger = LoggerManager.getLogger("ProcessTerminal")

suspend fun executeTerminal(
    project: Project,
    command: String,
    options: TerminalOptions,
    workingDir: String? = null,
    timeoutMillis: Long = 5 * 60_000,
): TerminalResult {
    val factory = AIMIConfigurationFactory.instance
    val configuration = factory.createTemplateConfiguration(project)

    // 配置参数
    configuration.command = command
    configuration.workingDirectory = workingDir ?: determineWorkingDirectory(workingDir, project)

    // 执行配置
    val executor = DefaultRunExecutor.getRunExecutorInstance()
    val builder = ExecutionEnvironmentBuilder.create(executor, configuration)

    // 创建一个CompletableDeferred来等待 ProcessHandler, 确认创建完成
    val processHandlerDeferred = CompletableDeferred<ProcessHandler>()
    val environment = builder.build() { descriptor ->
        configuration.terminalId = descriptor.executionId
        val info = AIMITerminalInfo.fromConfiguration(configuration)
        descriptor.processHandler?.let { processHandler ->
            info.attach(processHandler, project = project)
            processHandlerDeferred.complete(processHandler)
        }
        processHandlerDeferred.completeExceptionally(Throwable("Can not find process handler!"))
    }

    // 启动任务
    ExecutionManager.getInstance(project).restartRunProfile(environment)

    // 等待 ProcessHandler 并获取相关信息
    val processHandler = runCatching {
        processHandlerDeferred.await()
    }.getOrElse {
        return TerminalResult(
            output = "Can not create a process to execute shell script!",
            exitCode = -1,
            isTerminated = false,
            terminalId = -1,
        )
    }

    val processTerminalInfo = processHandler.getUserData(PROCESS_TERMINAL_KEY)
        ?: AIMITerminalInfo.findTerminalInfo(project, configuration.terminalId)
        ?: run {
            return TerminalResult(
                output = "Can not find terminal info after process prepared",
                exitCode = -1,
                isTerminated = false,
                terminalId = -1,
            )
        }
    if (options.waitForCompletion) {
        processTerminalInfo.waitFor(timeoutMillis)
        return TerminalResult(
            output = processTerminalInfo.getTerminalOutput(),
            exitCode = processTerminalInfo.exitCode,
            isTerminated = !processTerminalInfo.isRunning(),
            terminalId = processTerminalInfo.terminalId,
        )
    } else {
        return TerminalResult(
            output = processTerminalInfo.getTerminalOutput(),
            exitCode = processTerminalInfo.exitCode,
            isTerminated = false,
            terminalId = processTerminalInfo.terminalId,
        )
    }
}

private fun determineWorkingDirectory(cwd: String?, project: Project): String? {
    if (cwd != null && !cwd.trim { it <= ' ' }.isEmpty()) {
        val workingDir = File(cwd)
        if (workingDir.exists() && workingDir.isDirectory()) {
            return cwd
        }
    }
    val projectRoot = project.guessProjectDir()
    return projectRoot?.path
}
package com.taobao.mc.aimi.logger

import com.intellij.openapi.diagnostic.LogLevel
import com.intellij.openapi.diagnostic.Logger
import java.lang.invoke.MethodHandles

object LoggerManager {
    private val isDebugMode = System.getenv("USE_TCP")?.toBoolean() ?: false
    private const val MODULE = "AIMI-IDE"

    fun getLogger(tag: String): Logger {
        return Logger.getInstance("${MODULE}.${tag}").apply {
            if (isDebugMode) setLevel(LogLevel.DEBUG)
        }
    }

    fun getLogger(clazz: Class<*>): Logger {
        return getLogger(clazz.simpleName)
    }
}

// logger
inline fun <reified T : Any> logger(): Logger = LoggerManager.getLogger(T::class.java)

// thisLogger
inline fun <reified T : Any> T.thisLogger(): Logger = LoggerManager.getLogger(T::class.java)

// fileLogger
fun fileLogger(): Logger {
    val clazz = MethodHandles.lookup().lookupClass()
    return LoggerManager.getLogger(clazz)
}
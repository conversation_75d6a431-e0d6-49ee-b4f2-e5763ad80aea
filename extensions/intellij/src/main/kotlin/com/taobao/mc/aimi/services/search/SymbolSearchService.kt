package com.taobao.mc.aimi.services.search

import com.intellij.ide.actions.searcheverywhere.*
import com.intellij.ide.util.gotoByName.GotoClassSymbolConfiguration
import com.intellij.ide.util.gotoByName.LanguageRef
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.Service
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.impl.light.LightElement
import com.intellij.util.application
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.SymbolInfo
import com.taobao.mc.aimi.util.Reflect
import kotlinx.coroutines.isActive
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.Executor
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

@Service(Service.Level.PROJECT)
class SymbolSearchService : DumbAware {
    private val logger = LoggerManager.getLogger(javaClass)

    companion object {
        private const val DEFAULT_RESULT_LIMIT = 5
        private const val GROUPED_RESULTS_SEARCHER_CLASS = "com.intellij.ide.actions.searcheverywhere.GroupedResultsSearcher"
        private const val MY_FILTER_FIELD = "myFilter"
        private const val GET_TEXT_METHOD = "getText"
        private const val GET_CONTAINING_FILE_METHOD = "getContainingFile"
        private const val GET_PACKAGE_NAME_METHOD = "getPackageName"
        private const val GET_VIRTUAL_FILE_METHOD = "getVirtualFile"
        private const val SEARCH_METHOD = "search"
        private val SUPPORTED_LANGUAGE_IDS = listOf("kotlin", "knd", "java")
    }

    suspend fun searchSymbols(project: Project, searchPattern: String, cursorIndex: Int, anActionEvent: AnActionEvent): List<SymbolInfo> {
        return suspendCancellableCoroutine { continuation ->
            var progressIndicator: ProgressIndicator? = null

            try {
                val contributor = createSymbolContributor(project, anActionEvent)
                val contributorsMap = mapOf(contributor to DEFAULT_RESULT_LIMIT)
                val searchCollector = SearchResultCollector()
                val searchListener = createSearchListener(searchCollector) { error ->
                    finishSearch(continuation, searchCollector, error, progressIndicator)
                }

                val searcher = createGroupedResultsSearcher(project, searchListener, contributor)
                val queryStr = QueryPatternExtractor.extractQueryFromPattern(searchPattern, cursorIndex)

                progressIndicator = searcher.call(SEARCH_METHOD, contributorsMap, queryStr).get()

                continuation.invokeOnCancellation {
                    logger.info("Search cancelled by coroutine")
                    progressIndicator?.cancel()
                }

            } catch (error: Throwable) {
                logger.warn("Error starting search", error)
                finishSearchWithError(continuation, error)
            }
        }
    }

    private fun createSymbolContributor(project: Project, anActionEvent: AnActionEvent): SymbolSearchEverywhereContributor {
        return object : SymbolSearchEverywhereContributor(anActionEvent) {
            init {
                try {
                    val languageItems = LanguageRef.forAllLanguages()
                        .filter { SUPPORTED_LANGUAGE_IDS.contains(it.id.lowercase()) }
                    val persistentConfig = GotoClassSymbolConfiguration.getInstance(project)
                    val filter = PersistentSearchEverywhereContributorFilter(
                        languageItems,
                        persistentConfig,
                        LanguageRef::displayName,
                        LanguageRef::icon
                    )
                    Reflect.on(this).set(MY_FILTER_FIELD, filter)
                } catch (e: Exception) {
                    logger.warn("Failed to initialize symbol contributor filter", e)
                }
            }
        }.also { contributor ->
            logContributorInfo(contributor)
        }
    }

    private fun logContributorInfo(contributor: SymbolSearchEverywhereContributor) {
        try {
            val myFilter = Reflect.on(contributor).get<Any>(MY_FILTER_FIELD)
            logger.info("myScopeDescriptor: $myFilter")
        } catch (e: Exception) {
            logger.warn("Failed to log contributor info", e)
        }
    }

    private fun createSearchListener(
        collector: SearchResultCollector,
        onFinish: (Throwable?) -> Unit
    ): SearchListener {
        return object : SearchListener {
            private var startTime = 0L

            override fun elementsAdded(list: MutableList<out SearchEverywhereFoundElementInfo>) {
                collector.processResults(list)
            }

            override fun elementsRemoved(list: MutableList<out SearchEverywhereFoundElementInfo?>) {
                // Handle removed elements if needed
            }

            override fun contributorWaits(contributor: SearchEverywhereContributor<*>) {
                // Handle contributor waiting state if needed
            }

            override fun contributorFinished(contributor: SearchEverywhereContributor<*>, hasMore: Boolean) {
                logger.info("Contributor finished: ${contributor.searchProviderId}, hasMore: $hasMore")
            }

            override fun searchFinished(hasMoreMap: MutableMap<SearchEverywhereContributor<*>?, Boolean?>) {
                val searchTime = System.currentTimeMillis() - startTime
                logger.info("Search finished, cost: ${searchTime}ms, hasMore: $hasMoreMap, results: ${collector.getResultCount()}")
                onFinish(collector.getFirstError())
            }

            override fun searchStarted(pattern: String, contributors: MutableCollection<out SearchEverywhereContributor<*>?>) {
                logger.info("Search started: $pattern, contributors: $contributors")
                startTime = System.currentTimeMillis()
            }
        }
    }

    private fun createGroupedResultsSearcher(project: Project, searchListener: SearchListener, contributor: SymbolSearchEverywhereContributor): Reflect {
        val contributors = mutableListOf<SearchEverywhereContributor<*>>(contributor)
        val searchEverywhereUI = SearchEverywhereUI(project, contributors)
        val searchUI = Reflect.on(searchEverywhereUI)
        val mySearcher = searchUI.get<Any>("mySearcher")
        val onClass = Reflect.onClass(mySearcher.javaClass)
        return onClass.create(
            searchListener,
            createSearchExecutor(),
            SEResultsEqualityProvider.providers
        )
    }

    private fun createSearchExecutor(): Executor {
        return Executor { command ->
            application.invokeLater(command)
        }
    }

    private fun finishSearch(
        continuation: kotlin.coroutines.Continuation<List<SymbolInfo>>,
        collector: SearchResultCollector,
        error: Throwable?,
        progressIndicator: ProgressIndicator?
    ) {
        progressIndicator?.cancel()

        if (!continuation.context.isActive) return

        when {
            error != null && collector.isEmpty() -> continuation.resumeWithException(error)
            else -> continuation.resume(collector.getResults())
        }
    }

    private fun finishSearchWithError(
        continuation: kotlin.coroutines.Continuation<List<SymbolInfo>>,
        error: Throwable
    ) {
        if (continuation.context.isActive) {
            continuation.resumeWithException(error)
        }
    }

    private inner class SearchResultCollector {
        private val items = mutableListOf<SymbolInfo>()
        private var firstError: Throwable? = null

        fun processResults(list: MutableList<out SearchEverywhereFoundElementInfo>) {
            logger.info("processResults: ${list.size} results")
            for (info in list) {
                runCatching {
                    val element = info.getElement()
                    val symbolInfo = extractSymbolInfo(element)
                    val superclasses = mutableListOf<String>()
                    querySuperClasses(element.javaClass, superclasses)
                    symbolInfo.takeIf { it.filepath.isNotEmpty() }?.let { items.add(it) } ?: run {
                        logger.warn("Skipping empty result, element:${element}, classes: $superclasses")
                    }
                }.onFailure { error ->
                    logger.warn("Error processing search result", error)
                    if (firstError == null) {
                        firstError = error
                    }
                }
            }
        }

        private fun querySuperClasses(clazz: Class<*>, results: MutableList<String>) {
            results.add(clazz.simpleName)
            clazz.superclass?.let { querySuperClasses(it, results) }
        }

        private fun extractSymbolInfo(element: Any): SymbolInfo {
            if (element is LightElement) {
                val containingFile = element.containingFile
                val content = element.text
                val virtualFile = containingFile.virtualFile
                logger.info("Found symbol: $element, file: ${virtualFile?.url}, content: $content")
                return SymbolInfo(
                    filepath = virtualFile.url,
                    content = content
                )
            } else {
                runCatching {
                    val elementProxy = Reflect.on(element)
                    val content = elementProxy.call(GET_TEXT_METHOD).get<Any>().toString()
                    val containingFile = elementProxy.call(GET_CONTAINING_FILE_METHOD)
                    val pkgName = containingFile.call(GET_PACKAGE_NAME_METHOD).get<Any>() as String
                    val virtualFile = containingFile.call(GET_VIRTUAL_FILE_METHOD).get<VirtualFile>()
                    logger.info("Found symbol: $element, pkg: $pkgName, file: ${virtualFile?.url}, content: $content")
                    return SymbolInfo(
                        filepath = virtualFile.url,
                        content = content
                    )
                }
            }
            return SymbolInfo.EMPTY
        }

        fun getResults(): List<SymbolInfo> = items.toList()
        fun getResultCount(): Int = items.size
        fun isEmpty(): Boolean = items.isEmpty()
        fun getFirstError(): Throwable? = firstError
    }
}
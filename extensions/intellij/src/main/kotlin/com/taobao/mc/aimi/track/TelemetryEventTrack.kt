package com.taobao.mc.aimi.track

import com.intellij.openapi.components.service
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.taobao.mc.aimi.ext.autocomplete.PendingCompletion
import com.taobao.mc.aimi.ext.core.GitService
import com.taobao.mc.aimi.ext.core.UriUtils
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.services.TelemetryService
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import kotlinx.coroutines.launch

/**
 * 示例：如何使用 TelemetryService 的 eventTrack 方法上传埋点事件
 *
 * 这个类展示了如何正确调用 eventTrack 方法来上传各种类型的埋点事件
 */
object TelemetryEventTrack {

    /**
     * （用户代码改动）,主动光标变化。ide文件工作区切换
     * 记录光标变化的埋点事件
     * 1. 光标位置
     * 2. 文件路径
     * 3. scmBranch
     * 4. scmAddress
     * 4. 文件diff信息
     */
    fun trackCursorChange(
        project: Project,
        filepath: String,
        newPosition: LogicalPosition,
        oldFilepath: String?,
        oldPosition: LogicalPosition?
    ) {
        val telemetryService = project.service<TelemetryService>()
        val continuePluginService = project.service<AIMIPluginService>()
        continuePluginService.coroutineScope.launch {
            val gitService = project.service<GitService>()
            val ideProtocolClient = continuePluginService.awaitIdeProtocolClient()
            val projectPath = UriUtils.uriToFileSafe(project.guessProjectDir()?.toUriOrNull())
            val args = buildMap {
                put("codeContext", buildMap {
                    put("cursor_at", buildMap {
                        val fileDiff = gitService.getFileDiff(filepath) ?: ""
                        val scmBranch = ideProtocolClient.ide.getBranch(filepath)
                        val scmAddress = ideProtocolClient.ide.getRepoName(filepath)
                        val path = projectPath?.let {
                            UriUtils.uriToFileSafe(filepath)?.relativeTo(it)?.toString()
                        } ?: filepath
                        put("line", newPosition.line)
                        put("column", newPosition.column)
                        put("file_path", path)
                        put("scm_branch", scmBranch)
                        put("scm_address", scmAddress)
                        put("file_diff", fileDiff)
                    })
                    if (oldFilepath != null && oldPosition != null) {
                        val fileDiff = gitService.getFileDiff(oldFilepath) ?: ""
                        val scmBranch = ideProtocolClient.ide.getBranch(oldFilepath)
                        val scmAddress = ideProtocolClient.ide.getRepoName(oldFilepath)
                        val path = projectPath?.let {
                            UriUtils.uriToFileSafe(filepath)?.relativeTo(it)?.toString()
                        } ?: oldFilepath
                        put("old_cursor_at", buildMap {
                            put("line", oldPosition.line)
                            put("column", oldPosition.column)
                            put("file_path", path)
                            put("scm_branch", scmBranch)
                            put("scm_address", scmAddress)
                            put("file_diff", fileDiff)
                        })
                    }
                })
                put("eventType", "file_content_change")
            }
            telemetryService.eventTrack("file_content_change", args)
        }
    }


    /**
     * 代码补全触发开始事件
     */
    suspend fun trackAutocompleteInvokeStart(project: Project, filePath: String, localRequestId: String) {
        val telemetryService = project.service<TelemetryService>()
        val continuePluginService = project.service<AIMIPluginService>()
        val ideProtocolClient = continuePluginService.awaitIdeProtocolClient()
        val scmBranch = ideProtocolClient.ide.getBranch(filePath)
        val scmAddress = ideProtocolClient.ide.getRepoName(filePath)

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "autocomplete_invoke_start",
            "localRequestId" to localRequestId,
            "codeContext" to mapOf(
                "file_path" to filePath,
                "scm_branch" to scmBranch,
                "scm_address" to scmAddress
            )
        )

        telemetryService.eventTrack("autocomplete_invoke_start", args)
    }

    /**
     * 代码补全触发结束事件
     */
    fun trackAutocompleteInvokeEnd(project: Project, filePath: String, localRequestId: String) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "autocomplete_invoke_end",
            "localRequestId" to localRequestId,
            "codeContext" to mapOf(
                "file_path" to filePath
            )
        )

        telemetryService.eventTrack("autocomplete_invoke_end", args)
    }

    /**
     * 代码补全展示事件
     */
    fun trackAutocompleteDisplayed(project: Project, pendingCompletion: PendingCompletion?) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "autocomplete_displayed",
            "localRequestId" to (pendingCompletion?.completionId ?: ""),
            "requestId" to (pendingCompletion?.requestId ?: ""),
            "codeContext" to mapOf(
                "completionLineCount" to (pendingCompletion?.text ?: "").lines().count(),
                "file_path" to (pendingCompletion?.editor?.virtualFile?.toUriOrNull() ?: "")
            )
        )

        telemetryService.eventTrack("autocomplete_displayed", args)
    }

    /**
     * 代码补全采纳事件
     */
    fun trackAutocompleteAccept(project: Project, pendingCompletion: PendingCompletion?) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "autocomplete_accept",
            "localRequestId" to (pendingCompletion?.completionId ?: ""),
            "requestId" to (pendingCompletion?.requestId ?: ""),
            "codeContext" to mapOf(
                "completionLineCount" to (pendingCompletion?.text ?: "").lines().count(),
                "file_path" to (pendingCompletion?.editor?.virtualFile?.toUriOrNull() ?: "")
            )
        )

        telemetryService.eventTrack("autocomplete_accept", args)
    }

    /**
     * 应用到文件代码块触发展示事件
     */
    fun trackApplyToFileShow(project: Project, filePath: String?, localRequestId: String?, requestId: String?) {
        val telemetryService = project.service<TelemetryService>()
        val continuePluginService = project.service<AIMIPluginService>()
        continuePluginService.coroutineScope.launch {
            val ideProtocolClient = continuePluginService.awaitIdeProtocolClient()
            val scmBranch = filePath?.let { ideProtocolClient.ide.getBranch(filePath) }
            val scmAddress = filePath?.let { ideProtocolClient.ide.getRepoName(filePath) }

            val args = mapOf(
                "startTime" to System.currentTimeMillis(),
                "eventType" to "apply_to_file_show",
                "localRequestId" to (localRequestId ?: ""),
                "requestId" to (requestId ?: ""),
                "codeContext" to mapOf(
                    "file_path" to filePath,
                    "scm_branch" to scmBranch,
                    "scm_address" to scmAddress
                )
            )

            telemetryService.eventTrack("apply_to_file_show", args)
        }
    }

    /**
     * 应用到文件代码块同意事件
     */
    fun trackApplyToFileAccept(project: Project, filePath: String?, localRequestId: String?, requestId: String?, addLines: Int, deleteLines: Int) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "apply_to_file_accept",
            "localRequestId" to (localRequestId ?: ""),
            "requestId" to (requestId ?: ""),
            "codeContext" to mapOf(
                "addedLines" to addLines,
                "deleteLines" to deleteLines,
                "file_path" to filePath
            )
        )

        telemetryService.eventTrack("apply_to_file_accept", args)
    }

    /**
     * 应用到文件代码块拒绝事件
     */
    fun trackApplyToFileReject(project: Project, filePath: String?, localRequestId: String?, requestId: String?, addLines: Int, deleteLines: Int) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "apply_to_file_reject",
            "localRequestId" to (localRequestId ?: ""),
            "requestId" to (requestId ?: ""),
            "codeContext" to mapOf(
                "addedLines" to addLines,
                "deleteLines" to deleteLines,
                "file_path" to filePath
            )
        )

        telemetryService.eventTrack("apply_to_file_reject", args)
    }

    /**
     * 应用到文件全部同意事件
     */
    fun trackApplyToFileAcceptAll(project: Project, filePath: String?, localRequestId: String?, requestId: String?, addLines: Int, deleteLines: Int) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "apply_to_file_accept_all",
            "localRequestId" to (localRequestId ?: ""),
            "requestId" to (requestId ?: ""),
            "codeContext" to mapOf(
                "addedLines" to addLines,
                "deleteLines" to deleteLines,
                "file_path" to filePath
            )
        )

        telemetryService.eventTrack("apply_to_file_accept_all", args)
    }

    /**
     * 应用到文件全部拒绝事件
     */
    fun trackApplyToFileRejectAll(project: Project, filePath: String?, localRequestId: String?, requestId: String?, addLines: Int, deleteLines: Int) {
        val telemetryService = project.service<TelemetryService>()

        val args = mapOf(
            "startTime" to System.currentTimeMillis(),
            "eventType" to "apply_to_file_reject_all",
            "localRequestId" to (localRequestId ?: ""),
            "requestId" to (requestId ?: ""),
            "codeContext" to mapOf(
                "addedLines" to addLines,
                "deleteLines" to deleteLines,
                "file_path" to filePath
            )
        )

        telemetryService.eventTrack("apply_to_file_reject_all", args)
    }
}
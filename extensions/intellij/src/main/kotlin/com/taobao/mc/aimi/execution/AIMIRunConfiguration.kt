package com.taobao.mc.aimi.execution

import com.intellij.execution.Executor
import com.intellij.execution.configurations.RunConfiguration
import com.intellij.execution.configurations.RunConfigurationBase
import com.intellij.execution.configurations.RunConfigurationOptions
import com.intellij.execution.configurations.RunProfileState
import com.intellij.execution.runners.ExecutionEnvironment
import com.intellij.openapi.options.SettingsEditor
import com.intellij.openapi.project.Project
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBTextField
import com.intellij.util.ui.FormBuilder
import com.intellij.util.xml.Attribute
import javax.swing.JComponent


class AIMIRunConfiguration(
    project: Project,
    factory: AIMIConfigurationFactory,
    name: String
) : RunConfigurationBase<AIMIRunConfigurationOptions>(project, factory, name) {

    // 基本配置
    var command: String
        get() = options.commandOption ?: ""
        set(value) {
            options.commandOption = value
        }
    var workingDirectory: String?
        get() = options.workingDirectoryOption
        set(value) {
            options.workingDirectoryOption = value
        }
    var terminalId: Long
        get() = options.terminalIdOption
        set(value) {
            options.terminalIdOption = value
        }

    override fun getOptions(): AIMIRunConfigurationOptions {
        return super.getOptions() as AIMIRunConfigurationOptions
    }

    override fun getState(executor: Executor, environment: ExecutionEnvironment): RunProfileState {
        return AIMICommandLineState(environment, this)
    }

    override fun getConfigurationEditor(): SettingsEditor<out RunConfiguration?> {
        return AIMIRunConfigurationEditor(project)
    }
}

// 配置选项类
class AIMIRunConfigurationOptions : RunConfigurationOptions() {
    // 可以添加持久化选项
    @get:Attribute("command")
    var commandOption: String? by string("")

    @get:Attribute("workingDirectory")
    var workingDirectoryOption: String? by string("")

    @get:Attribute("terminalId")
    var terminalIdOption: Long by property(0L)
}

// 配置编辑器
class AIMIRunConfigurationEditor(private val project: Project) : SettingsEditor<AIMIRunConfiguration>() {

    private val commandField = JBTextField()
    private val workingDirectoryField = JBTextField()

    override fun resetEditorFrom(configuration: AIMIRunConfiguration) {
        commandField.text = configuration.command
        workingDirectoryField.text = configuration.workingDirectory
    }

    override fun applyEditorTo(configuration: AIMIRunConfiguration) {
        configuration.command = commandField.text
        configuration.workingDirectory = workingDirectoryField.text
    }

    override fun createEditor(): JComponent {
        return FormBuilder.createFormBuilder()
            .addLabeledComponent(JBLabel("Command:"), commandField)
            .addLabeledComponent(JBLabel("WorkingDirectory:"), workingDirectoryField)
            .panel
    }
}
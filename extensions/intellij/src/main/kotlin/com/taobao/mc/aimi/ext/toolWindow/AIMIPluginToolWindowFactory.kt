package com.taobao.mc.aimi.ext.toolWindow

import ai.grazie.utils.capitalize
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.DefaultActionGroup
import com.intellij.openapi.components.service
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.openapi.wm.impl.content.ToolWindowContentUi
import com.intellij.ui.content.Content
import com.intellij.ui.content.ContentManagerEvent
import com.intellij.ui.content.ContentManagerListener
import com.intellij.ui.dsl.builder.panel
import com.intellij.ui.jcef.JBCefApp
import com.intellij.ui.jcef.executeJavaScript
import com.intellij.util.ui.JBUI.Borders.empty
import com.taobao.mc.aimi.actions.*
import com.taobao.mc.aimi.ext.constants.AIMIConstants
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.WindowType
import com.taobao.mc.aimi.util.Reflect
import com.taobao.mc.aimi.util.URLManager
import com.taobao.mc.aimi.util.type
import javax.swing.JComponent
import javax.swing.JPanel

const val JS_QUERY_POOL_SIZE = "200"

class AIMIPluginToolWindowFactory : ToolWindowFactory, DumbAware {
    private val logger = LoggerManager.getLogger(AIMIPluginToolWindowFactory::class.java)
    private val isDebugMode = !System.getenv("ORG_GRADLE_PROJECT_platformVersion").isNullOrEmpty()

    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        if (!JBCefApp.isSupported()) {
            val panel = createNotSupportedPanel()
            val contentManager = toolWindow.contentManager
            val content = contentManager.factory.createContent(panel, AIMIConstants.PLUGIN_NAME, false)
            contentManager.addContent(content)
            return
        }

        val continuePluginService = project.service<AIMIPluginService>()
        continuePluginService.toolWindow = toolWindow

        // 只创建常驻的Chat窗口
        val chatWindow = AIMIPluginWindow(project, "AIMI_URL", false)
        chatWindow.preparePanel(toolWindow, WindowType.Chat)
        continuePluginService.addResidentWindow(chatWindow, WindowType.Chat)

        // 非常驻窗口不再预创建，将在需要时动态创建
        logger.info("Initialized tool window with resident Chat window only. Non-resident windows will be created on demand.")

        toolWindow.addContentManagerListener(object : ContentManagerListener {
            override fun selectionChanged(event: ContentManagerEvent) {
                val windowType = event.content.type ?: return
                continuePluginService.onPluginWindowChanged(windowType)
            }

            override fun contentRemoved(event: ContentManagerEvent) {
                val windowType = event.content.type ?: return
                continuePluginService.closeNonResidentWindow(windowType)
            }
        })

        val titleActions = mutableListOf<AnAction>()
        createTitleActions(titleActions)

        // Add MaximizeToolWindow action
        // val action = ActionManager.getInstance().getAction("MaximizeToolWindow")
        // if (action != null) {
        //     titleActions.add(action)
        // }

        toolWindow.setTitleActions(titleActions)

        // 设置additionalGearActions，包含开发者工具
        val devToolsAction = ActionManager.getInstance().getAction("aimi.openDevTools")
        if (devToolsAction != null) {
            val gearActionGroup = DefaultActionGroup()
            gearActionGroup.add(devToolsAction)
            addDebugActions(gearActionGroup)
            toolWindow.setAdditionalGearActions(gearActionGroup)
        }

        runCatching {
            Reflect.on(toolWindow).set("canCloseContent", true)
            toolWindow.component.putClientProperty(ToolWindowContentUi.HIDE_ID_LABEL, "true")
            toolWindow.component.putClientProperty(ToolWindowContentUi.DONT_HIDE_TOOLBAR_IN_HEADER, true)
        }.onFailure {
            logger.warn("Failed to set tool window properties", it)
        }
    }

    private fun createNotSupportedPanel(): JPanel {
        val html = AIMIPluginToolWindowFactory::class.java.classLoader
            .getResourceAsStream("jcef_error.html").let {
                if (it == null) throw RuntimeException("不支持JCEF浏览器环境")
                it
            }
            .bufferedReader()
            .readText()
        return panel {
            row {
                text(html)
            }
        }.withBorder(empty(10))
    }

    private fun createTitleActions(titleActions: MutableList<in AnAction>) {
        ActionManager.getInstance().getAction("aimi.SidebarActionsGroup")?.let(titleActions::add)
    }

    private fun addDebugActions(additionalAction: DefaultActionGroup) {
        if (!isDebugMode) return
        additionalAction.add(OpenAPIWindowAction())
        additionalAction.add(OpenContinueWindowAction())
        additionalAction.add(SearchAction())
        additionalAction.add(RipGrepSearchAction())
        additionalAction.add(TestUIFreezeAction())
        additionalAction.add(SmartCompletionAction())
        additionalAction.add(TestExecuteProcessTerminalAction())
    }

    override fun shouldBeAvailable(project: Project) = true

    class AIMIPluginWindow(private val project: Project, private val urlKey: String, val closeable: Boolean = true) {
        private val logger = LoggerManager.getLogger(AIMIPluginWindow::class.java)
        private var lastEnvironment: String = URLManager.getCurrentEnvironment()

        init {
            System.setProperty("ide.browser.jcef.jsQueryPoolSize", JS_QUERY_POOL_SIZE)
            System.setProperty("ide.browser.jcef.contextMenu.devTools.enabled", "true")
        }

        val browser: AIMIBrowser by lazy {
            val url = URLManager.getUrl(urlKey)
            logger.info("$urlKey URL: $url")
            AIMIBrowser(project, url)
        }

        val content: JComponent
            get() = browser.browser.component

        var contentWrapper: Content? = null

        fun changeTitle(title: String) {
            contentWrapper?.displayName = title
        }

        /**
         * 检查并更新URL（当环境设置变更时）
         */
        suspend fun checkAndUpdateUrl() {
            if (URLManager.hasEnvironmentChanged(lastEnvironment)) {
                val newUrl = URLManager.getUrl(urlKey)
                logger.info("Environment changed:${lastEnvironment} to ${URLManager.getCurrentEnvironment()}, updating $urlKey URL to: $newUrl")

                // 如果浏览器已经初始化，则刷新到新URL
                browser.url = newUrl
                reload(newUrl)

                // 更新最后的环境状态
                lastEnvironment = URLManager.getCurrentEnvironment()
            }
        }

        suspend fun reload(url: String? = null) {
            runCatching {
                if (!url.isNullOrEmpty()) {
                    browser.browser.executeJavaScript("window.location.href= '$url';")
                } else {
                    browser.browser.executeJavaScript("window.location.reload();")
                }
            }.onFailure {
                logger.warn("reload browser: $urlKey failed", it)
            }
        }

        fun preparePanel(toolWindow: ToolWindow, type: WindowType): Content {
            val content = addContent(toolWindow, content, type.type.capitalize())
            content.type = type
            contentWrapper = content
            return content
        }

        private fun addContent(toolWindow: ToolWindow, panel: JComponent?, displayName: String?): Content {
            val contentManager = toolWindow.contentManager
            val content = contentManager.factory.createContent(panel, displayName, false)
            content.isCloseable = closeable
            contentManager.addContent(content)
            return content
        }
    }
}
package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.ui.Messages
import com.taobao.mc.aimi.execution.executeTerminal
import com.taobao.mc.aimi.ext.protocol.TerminalOptions
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 测试executeProcessTerminal方法的Action
 * 用于调用 com.taobao.mc.aimi.execution.AIMIExecuteUtilKt#executeProcessTerminal
 */
class TestExecuteProcessTerminalAction : AnAction("测试执行进程终端") {

    private val logger = LoggerManager.getLogger(javaClass)
    private val coroutineScope = CoroutineScope(Dispatchers.IO)

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return

        // 询问用户要执行的命令
        val command = Messages.showInputDialog(
            project,
            "请输入要执行的命令:",
            "测试执行进程终端",
            Messages.getQuestionIcon(),
            "echo 'Hello from executeProcessTerminal!'", // 默认命令
            null
        )

        if (command.isNullOrBlank()) {
            Messages.showWarningDialog(
                project,
                "命令不能为空",
                "输入错误"
            )
            return
        }

        // 询问是否等待命令完成
        val waitForCompletion = true

        // 询问超时时间
        val timeoutMillis = 30000L

        logger.info("开始执行命令: $command, 等待完成: $waitForCompletion, 超时: ${timeoutMillis}ms")

        // 在后台线程中执行命令
        coroutineScope.launch {
            try {
                val terminalOptions = TerminalOptions(
                    reuseTerminal = false,
                    terminalName = "Test-ProcessTerminal",
                    waitForCompletion = waitForCompletion
                )

                val result = executeTerminal(
                    project = project,
                    command = command,
                    options = terminalOptions,
                    timeoutMillis = timeoutMillis
                )
                result.output.lines().forEach(logger::debug)

                // 在EDT线程中显示结果
                /*ApplicationManager.getApplication().invokeLater {
                    val message = buildString {
                        appendLine("命令执行结果:")
                        appendLine("命令: $command")
                        appendLine("终端ID: ${result.terminalId}")
                        appendLine("状态: ${result.statusDescription}")
                        appendLine("是否终止: ${result.isTerminated}")
                        appendLine("退出代码: ${result.exitCode}")
                        appendLine("执行成功: ${result.isSuccess}")
                        appendLine()
                        appendLine("输出内容:")
                        appendLine("=".repeat(50))
                        appendLine(result.output.ifEmpty { "(无输出)" })
                        appendLine("=".repeat(50))
                    }

                    logger.info("命令执行完成: ${result.statusDescription}")
                    
                    Messages.showInfoMessage(
                        project,
                        message,
                        "executeProcessTerminal 执行结果"
                    )
                }*/

            } catch (e: Exception) {
                logger.error("执行命令时发生异常", e)

                ApplicationManager.getApplication().invokeLater {
                    Messages.showErrorDialog(
                        project,
                        "执行命令时发生异常:\n${e.message}",
                        "执行错误"
                    )
                }
            }
        }
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}
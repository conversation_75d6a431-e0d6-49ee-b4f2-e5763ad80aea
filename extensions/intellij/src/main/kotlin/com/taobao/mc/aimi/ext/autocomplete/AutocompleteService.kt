package com.taobao.mc.aimi.ext.autocomplete

import com.intellij.injected.editor.VirtualFileWindow
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.InlayProperties
import com.intellij.openapi.editor.impl.EditorImpl
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.wm.WindowManager
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiElement
import com.intellij.util.application
import com.taobao.mc.aimi.ext.ToastType
import com.taobao.mc.aimi.ext.actions.toggleAIMI
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.ext.utils.uuid
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.track.TelemetryEventTrack
import com.taobao.mc.aimi.types.AutocompleteOutcome
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.util.childScope
import com.taobao.mc.aimi.util.kGson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

data class PendingCompletion(
    val editor: Editor,
    var offset: Int,
    val completionId: String,
    var text: String?,
    var requestId: String?,
    // 添加原始完整补全文本，用于智能复用
    var originalFullText: String? = null,
    // 添加原始触发位置，用于计算用户已输入的内容
    var originalOffset: Int = offset
)

fun PsiElement.isInjectedText(): Boolean {
    val virtualFile = this.containingFile.virtualFile ?: return false
    if (virtualFile is VirtualFileWindow) {
        return true
    }
    return false
}

fun Editor.addInlayElement(
    lines: List<String>,        // 要显示的文本行列表
    offset: Int,               // 在编辑器中的插入位置偏移量
    properties: InlayProperties // Inlay 显示属性配置
) {
    // 检查当前编辑器是否为 EditorImpl 类型（IntelliJ 的具体编辑器实现）
    if (this is EditorImpl) {
        // 如果第一行不为空，则添加内联元素（在当前行内显示）
        if (lines[0].isNotEmpty()) {
            inlayModel.addInlineElement(
                offset,                              // 插入位置
                properties,                          // 显示属性
                AIMIInlayRenderer(listOf(lines[0])) // 使用自定义渲染器显示第一行
            )
        }
        // 如果有多行内容，则添加块级元素（在行间显示剩余行）
        if (lines.size > 1) {
            inlayModel.addBlockElement(
                offset,                           // 插入位置
                properties,                       // 显示属性
                AIMIInlayRenderer(lines.drop(1)) // 使用自定义渲染器显示除第一行外的所有行
            )
        }
    }
}

@Service(Service.Level.PROJECT)
class AutocompleteService(private val project: Project) {
    private val logger = LoggerManager.getLogger(AutocompleteService::class.java)
    private val aimiService by lazy { project.service<AIMIPluginService>() }
    private var checkLoginScope: CoroutineScope? = null
    var pendingCompletion: PendingCompletion? = null
    private val autocompleteLookupListener = project.service<AutocompleteLookupListener>()
    private val widget: AutocompleteSpinnerWidget? by lazy {
        WindowManager.getInstance().getStatusBar(project)
            ?.getWidget(AutocompleteSpinnerWidget.ID) as? AutocompleteSpinnerWidget
    }

    // To avoid triggering another completion on partial acceptance,
    // we need to keep track of whether the last change was a partial accept
    var lastChangeWasPartialAccept = false

    /**
     * 智能补全复用逻辑：检查用户输入是否与当前补全匹配
     * 如果匹配，则复用当前补全并截取剩余内容
     * @param editor 编辑器
     * @return true 如果成功复用了补全，false 如果需要重新请求补全
     */
    fun tryReuseCompletion(editor: Editor): Boolean {
        val pending = pendingCompletion ?: return false
        val originalFullText = pending.originalFullText ?: return false

        return runReadAction {
            try {
                val currentOffset = editor.caretModel.offset
                val originalOffset = pending.originalOffset

                // 计算用户已输入的字符数
                val typedLength = currentOffset - originalOffset
                if (typedLength <= 0) return@runReadAction false

                // 获取用户已输入的内容
                val typedText = editor.document.getText(TextRange(originalOffset, currentOffset))

                // 检查用户输入是否与补全内容的前缀匹配
                if (originalFullText.startsWith(typedText)) {
                    // 计算剩余的补全内容
                    val remainingCompletion = originalFullText.substring(typedLength)

                    if (remainingCompletion.isNotEmpty()) {
                        logger.info("[AutocompleteReuse] 复用补全成功，已输入: '$typedText'，剩余: '$remainingCompletion'")

                        // 更新 pending completion
                        pending.text = remainingCompletion
                        pending.offset = currentOffset

                        // 同步渲染剩余的补全内容，避免双重异步调用
                        invokeLater {
                            // 先清除现有的补全显示
                            hideCompletions(editor)

                            // 直接调用渲染逻辑，而不是再次调用 renderCompletion
                            if (!isInjectedFile(editor) && !shouldSkipRender()) {
                                val properties = InlayProperties()
                                properties.relatesToPrecedingText(true)
                                properties.disableSoftWrapping(true)

                                val lines = remainingCompletion.lines()
                                pendingCompletion = pendingCompletion?.copy(text = lines.joinToString("\n"))
                                editor.addInlayElement(lines, currentOffset, properties)

                                // 埋点: autocomplete_displayed
                                TelemetryEventTrack.trackAutocompleteDisplayed(project, pendingCompletion)
                            }
                        }

                        return@runReadAction true
                    }
                }

                logger.info("[AutocompleteReuse] 无法复用补全，用户输入: '$typedText'，补全前缀不匹配")
                return@runReadAction false

            } catch (e: Exception) {
                logger.warn("[AutocompleteReuse] 复用补全时发生异常: ${e.message}")
                return@runReadAction false
            }
        }
    }

    fun triggerCompletion(editor: Editor, force: Boolean = false) {
        val settings = application.service<AIMISettingService>()
        if (!force && !settings.state.enableTabAutocomplete) {
            return
        }

        if (!isLogin()) {
            checkLoginScope?.cancel()
            checkLoginScope = aimiService.coroutineScope.childScope("checkLogin", Dispatchers.EDT)
            checkLoginScope?.launch {
                val ideClient = aimiService.awaitIdeProtocolClient()
                val ide = ideClient.ide
                val btnTxt = "去登录"
                val button = ide.showToast(
                    type = ToastType.ERROR,
                    "补全不可用,请先登录",
                    btnTxt
                )
                logger.warn("Autocomplete is disabled due to not being logged in.  Received: $button")
                if (button == btnTxt) {
                    invokeLater {
                        toggleAIMI(project, false)
                    }
                }
            }
            return
        }

        if (isNearImportOrPackage(editor)) {
            return
        }

        val diffStreamService = project.service<DiffStreamService>()
        if (diffStreamService.isRunning(editor)) {
            return
        }

        // 尝试复用当前补全
        if (!force && tryReuseCompletion(editor)) {
            logger.info("[AutocompleteReuse] 成功复用补全，跳过新的补全请求")
            return
        }


        if (pendingCompletion != null) {
            clearCompletions(pendingCompletion!!.editor)
        }

        // Set pending completion
        val completionId = uuid()
        val offset = editor.caretModel.primaryCaret.offset
        pendingCompletion = PendingCompletion(editor, offset, completionId, null, null, null, offset)

        // Request a completion from the ext
        val virtualFile = FileDocumentManager.getInstance().getFile(editor.document)

        val uri = virtualFile?.toUriOrNull() ?: return

        aimiService.coroutineScope.launch {
            // 埋点: autocomplete_invoke_start
            TelemetryEventTrack.trackAutocompleteInvokeStart(project, uri, completionId)

            widget?.setLoading(true)

            val line = editor.caretModel.primaryCaret.logicalPosition.line
            val column = editor.caretModel.primaryCaret.logicalPosition.column
            val input = mapOf(
                "completionId" to completionId,
                "filepath" to uri,
                "pos" to mapOf(
                    "line" to line,
                    "character" to column
                ),
                "clipboardText" to "",
                "recentlyEditedRanges" to emptyList<Any>(),
                "recentlyVisitedRanges" to emptyList<Any>(),
                "force" to force,
            )

            project.service<AIMIPluginService>().coreMessenger?.request(
                MessageTypes.ToCore.AutocompleteComplete,
                input,
                null,
                ({ response ->
                    if (pendingCompletion == null || pendingCompletion?.completionId == completionId) {
                        widget?.setLoading(false)
                    }

                    val outcomeJson = response?.get("content") ?: return@request
                    val outcome = kGson.fromJson(outcomeJson, AutocompleteOutcome::class.java)

                    if (!outcome.completion.isNullOrEmpty()) {
                        val completion = outcome.completion
                        // val finalTextToInsert = deduplicateCompletion(editor, offset, completion)
                        val finalTextToInsert = completion

                        if (shouldRenderCompletion(finalTextToInsert, offset, line, editor)) {
                            pendingCompletion = PendingCompletion(
                                editor,
                                offset,
                                completionId,
                                finalTextToInsert,
                                outcome.remoteRequestId,
                                finalTextToInsert, // 保存原始完整补全文本
                                offset // 保存原始触发位置
                            )
                            renderCompletion(editor, offset, finalTextToInsert)
                        }
                    }
                })
            )

            // 埋点: autocomplete_invoke_end
            TelemetryEventTrack.trackAutocompleteInvokeEnd(project, uri, completionId)
        }
    }

    private fun shouldRenderCompletion(completion: String, offset: Int, line: Int, editor: Editor): Boolean {
        if (completion.isEmpty() || runReadAction { offset != editor.caretModel.offset }) {
            return false
        }

        if (completion.lines().size == 1) {
            return true
        }

        val endOffset = editor.document.getLineEndOffset(line)

        // Do not render if completion is multi-line and caret is in middle of line
        return offset <= endOffset && editor.document.getText(TextRange(offset, endOffset)).isBlank()
    }

    private fun deduplicateCompletion(editor: Editor, offset: Int, completion: String): String {
        // Check if completion matches the first 10 characters after the cursor
        return ApplicationManager.getApplication().runReadAction<String> {
            val document = editor.document
            val caretOffset = editor.caretModel.offset

            // Don't care about it if it's at the end of the document
            if (caretOffset == document.textLength) return@runReadAction completion

            val N = 10
            var textAfterCursor = if (caretOffset + N <= document.textLength) {
                document.getText(TextRange(caretOffset, caretOffset + N))
            } else {
                document.getText(TextRange(caretOffset, document.textLength))
            }

            // Avoid truncating the completion text when the text after the cursor is blank
            if (textAfterCursor.isBlank()) return@runReadAction completion

            // Determine the index of a newline character within the text following the cursor.
            val newlineIndex = textAfterCursor.indexOf("\r\n").takeIf { it >= 0 } ?: textAfterCursor.indexOf('\n')
            // If a newline character is found and the current line is not empty, truncate the text at that point.
            if (newlineIndex > 0) {
                textAfterCursor = textAfterCursor.substring(0, newlineIndex)
            }

            val indexOfTextAfterCursorInCompletion = completion.indexOf(textAfterCursor)
            if (indexOfTextAfterCursorInCompletion > 0) {
                return@runReadAction completion.slice(0..indexOfTextAfterCursorInCompletion - 1)
            } else if (indexOfTextAfterCursorInCompletion == 0) {
                return@runReadAction ""
            }

            return@runReadAction completion
        }
    }

    private fun renderCompletion(editor: Editor, offset: Int, completion: String) {
        // Don't render if completion is empty
        if (completion.isEmpty()) {
            return
        }

        if (isInjectedFile(editor)) return
        // Skip rendering completions if the code completion dropdown is already visible and the IDE completion side-by-side setting is disabled
        if (shouldSkipRender()) {
            return
        }

        // 埋点: autocomplete_displayed
        TelemetryEventTrack.trackAutocompleteDisplayed(project, pendingCompletion)
        invokeLater {
            // Clear existing completions
            hideCompletions(editor)

            val properties = InlayProperties()
            properties.relatesToPrecedingText(true)
            properties.disableSoftWrapping(true)

            val lines = completion.lines()
            pendingCompletion = pendingCompletion?.copy(text = lines.joinToString("\n"))
            editor.addInlayElement(lines, offset, properties)

//                val attributes = TextAttributes().apply {
//                    backgroundColor = JBColor.GREEN
//                }
//                val key = TextAttributesKey.createTextAttributesKey("CONTINUE_AUTOCOMPLETE")
//                key.let { editor.colorsScheme.setAttributes(it, attributes) }
//                editor.markupModel.addLineHighlighter(key, editor.caretModel.logicalPosition.line, HighlighterLayer.LAST)
        }
    }

    fun accept() {
        val completion = pendingCompletion ?: return
        val text = completion.text ?: return
        val editor = completion.editor
        val offset = completion.offset
        editor.document.insertString(offset, text)

        editor.caretModel.moveToOffset(offset + text.length)

        project.service<AIMIPluginService>().coreMessenger?.request(
            MessageTypes.ToCore.AutocompleteAccept,
            hashMapOf("completionId" to completion.completionId),
            completion.completionId,
            ({})
        )
        invokeLater {
            clearCompletions(editor, completion)
        }

        // 埋点:autocomplete_accept
        TelemetryEventTrack.trackAutocompleteAccept(project, completion)
    }

    private fun shouldSkipRender(): Boolean {
        val settings = service<AIMISettingService>()
        return !settings.state.showIDECompletionSideBySide && !autocompleteLookupListener.isLookupEmpty()
    }

    private fun splitKeepingDelimiters(input: String, delimiterPattern: String = "\\s+"): List<String> {
        val initialSplit = input.split("(?<=$delimiterPattern)|(?=$delimiterPattern)".toRegex())
            .filter { it.isNotEmpty() }

        val result = mutableListOf<String>()
        var currentDelimiter = ""

        for (part in initialSplit) {
            if (part.matches(delimiterPattern.toRegex())) {
                currentDelimiter += part
            } else {
                if (currentDelimiter.isNotEmpty()) {
                    result.add(currentDelimiter)
                    currentDelimiter = ""
                }
                result.add(part)
            }
        }

        if (currentDelimiter.isNotEmpty()) {
            result.add(currentDelimiter)

        }

        return result
    }

    fun partialAccept() {
        val completion = pendingCompletion ?: return
        val text = completion.text ?: return
        val editor = completion.editor
        val offset = completion.offset

        lastChangeWasPartialAccept = true

        // Split the text into words, keeping delimiters
        val words = splitKeepingDelimiters(text)
        logger.info("$words")
        val word = words[0]
        editor.document.insertString(offset, word)
        editor.caretModel.moveToOffset(offset + word.length)

        // Remove the completion and re-display it
        hideCompletions(editor)
        completion.text = text.substring(word.length)
        completion.offset += word.length
        renderCompletion(editor, completion.offset, completion.text!!)
    }

    private fun cancelCompletion(completion: PendingCompletion) {
        // Send cancellation message to ext
        widget?.setLoading(false)
        project.service<AIMIPluginService>().coreMessenger?.request(MessageTypes.ToCore.AutocompleteCancel, null, null, ({}))
    }

    fun clearCompletions(editor: Editor, completion: PendingCompletion? = pendingCompletion) {
        if (isInjectedFile(editor)) return

        if (completion != null) {
            cancelCompletion(completion)
            if (completion.completionId == pendingCompletion?.completionId) pendingCompletion = null
        }
        disposeInlayRenderer(editor)
    }

    private fun isInjectedFile(editor: Editor): Boolean {
        return runReadAction {
            PsiDocumentManager.getInstance(project).getPsiFile(editor.document)?.isInjectedText() ?: false
        }
    }

    fun hideCompletions(editor: Editor) {
        if (isInjectedFile(editor)) return

        disposeInlayRenderer(editor)
    }

    private fun disposeInlayRenderer(editor: Editor) {
        editor.inlayModel.getInlineElementsInRange(0, editor.document.textLength).forEach {
            if (it.renderer is AIMIInlayRenderer) {
                it.dispose()
            }
        }
        editor.inlayModel.getBlockElementsInRange(0, editor.document.textLength).forEach {
            if (it.renderer is AIMIInlayRenderer) {
                it.dispose()
            }
        }
    }

    private fun isNearImportOrPackage(editor: Editor): Boolean {
        return runReadAction {
            val document = editor.document
            val caretOffset = editor.caretModel.offset
            val currentLine = document.getLineNumber(caretOffset)

            // 检查当前行上方1-2行
            for (i in 0..2) {
                val lineNumber = currentLine - i
                if (lineNumber >= 0) {
                    val lineStartOffset = document.getLineStartOffset(lineNumber)
                    val lineEndOffset = document.getLineEndOffset(lineNumber)
                    val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset)).trim()

                    // 检查是否包含 import 或 package 关键字
                    if (lineText.startsWith("import ") || lineText.startsWith("package ")) {
                        return@runReadAction true
                    }
                }
            }
            false
        }
    }

    private fun isLogin(): Boolean {
        val userInfo = AIMISettingService.instance.state.userInfo
        return userInfo?.empId?.isNotEmpty() == true
    }
}
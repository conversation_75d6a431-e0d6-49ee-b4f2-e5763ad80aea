package com.taobao.mc.aimi.ext.autocomplete

import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorKind
import com.intellij.openapi.editor.event.*
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.taobao.mc.aimi.ext.actions.aimiService
import com.taobao.mc.aimi.ext.utils.Debouncer
import com.taobao.mc.aimi.ext.utils.castNestedOrNull
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.nextEdit.NextEditService
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.util.mSelectedTextEditor
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.plus

class AutocompleteCaretListener : CaretListener {
    override fun caretPositionChanged(event: CaretEvent) {
        if (event.editor.editorKind != EditorKind.MAIN_EDITOR) {
            return
        }

        val caret = event.caret ?: return
        val offset = caret.offset
        val editor = caret.editor
        val autocompleteService = editor.project?.service<AutocompleteService>() ?: return

        if (autocompleteService.lastChangeWasPartialAccept) {
            autocompleteService.lastChangeWasPartialAccept = false
            return
        }

        val pending = autocompleteService.pendingCompletion;
        if (pending != null && pending.editor == editor && pending.offset == offset) {
            return
        }
        autocompleteService.clearCompletions(editor)
    }
}

class AutocompleteDocumentListener(private val editorManager: FileEditorManager, private val editor: Editor) : DocumentListener {
    private val logger = LoggerManager.getLogger(javaClass)
    private val debounceScope = CoroutineScope(Dispatchers.Default) + CoroutineName(javaClass.simpleName)
    private val debouncer = Debouncer(AIMISettingService.instance.state.debounceDelay.toLong(), debounceScope)

    override fun documentChanged(event: DocumentEvent) {
        // Ignore empty changes or changes where content didn't actually change.
        if (event.newFragment.isEmpty() && event.oldFragment.isEmpty()) {
            return
        }

        // Make sure this is an actual text modification event.
        if (!event.isWholeTextReplaced && event.offset == 0 && event.oldLength == 0 && event.newLength == 0) {
            return
        }

        if (editor != editorManager.mSelectedTextEditor) {
            return
        }

        // 如果光标前后都有字符, 且在同一行, 不做处理
        if (shouldNotComplete(editor)) return

        val project = editor.project
        val service = project?.service<AutocompleteService>() ?: return
        if (service.lastChangeWasPartialAccept) {
            return
        }

        // 首先尝试复用当前补全
        // 如果用户输入的内容与当前补全匹配，直接复用而不触发新的补全请求
        if (service.tryReuseCompletion(editor)) {
            logger.info("[AutocompleteReuse] 文档变化时成功复用补全，跳过新的补全请求")
            return
        }

        val nextEditService = project.service<NextEditService>()

        // Check if we're in a test environment based on some property or condition
        val isAutocompleteTestEnvironment = System.getProperty("aimi.autocomplete.test.environment") == "true"
        val isNextEditTestEnvironment = System.getProperty("aimi.nextEdit.test.environment") == "true"

        if (isAutocompleteTestEnvironment) {
            invokeLater {
                service.triggerCompletion(editor)
            }
            return
        } else if (isNextEditTestEnvironment) {
            invokeLater {
                nextEditService.triggerNextEdit(editor)
            }
            return
        }

        // Check settings to see if next edit is enabled, and then trigger either autocomplete or next exit.
        val aimiService = project.aimiService


        debouncer.debounce {
            aimiService.coreMessenger?.request(
                MessageTypes.ToCore.ConfigGetSerializedProfileInfo,
                null,
                null,
                ({ response ->
                    val optInNextEditFeature = response.castNestedOrNull<Boolean>(
                        "content",
                        "result",
                        "config",
                        "experimental",
                        "optInNextEditFeature"
                    ) ?: false

                    invokeLater {
                        if (optInNextEditFeature) {
                            nextEditService.triggerNextEdit(editor)
                        } else {
                            // Invoke later is important, otherwise the completion will be triggered before the document is updated
                            // causing the old caret offset to be used
                            // TODO: concurrency
                            service.triggerCompletion(editor)
                        }
                    }
                })
            )
        }
    }

    private fun shouldNotComplete(editor: Editor): Boolean {
        val document = editor.document
        val caretOffset = editor.caretModel.offset
        val lineNumber = document.getLineNumber(caretOffset)
        val lineStartOffset = document.getLineStartOffset(lineNumber)
        val lineEndOffset = document.getLineEndOffset(lineNumber)

        // 检查光标前后是否都有字符
        val hasCharBefore = caretOffset > lineStartOffset &&
                document.text.getOrNull(caretOffset - 1)?.let { !it.isWhitespace() } == true
        val hasCharAfter = caretOffset < lineEndOffset &&
                document.text.getOrNull(caretOffset)?.let { !it.isWhitespace() } == true

        return hasCharBefore && hasCharAfter
    }
}

class AutocompleteEditorListener : EditorFactoryListener {
    private val logger = LoggerManager.getLogger(javaClass)
    private val disposables = mutableMapOf<Editor, () -> Unit>()
    override fun editorCreated(event: EditorFactoryEvent) {
        val editor = event.editor
        val project = editor.project ?: return
        val editorManager = project.let { FileEditorManager.getInstance(it) } ?: return
        val completionProvider = project.service<AutocompleteService>()

        // Listen to changes to mouse position
        val caretListener = AutocompleteCaretListener()
        editor.caretModel.addCaretListener(caretListener)

        // Listen to changes to selection
        val connection = editor.project?.messageBus?.connect()
        connection?.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
            override fun selectionChanged(event: FileEditorManagerEvent) {
                completionProvider.clearCompletions(editor)
            }
        })

        // Listen to changes to content
        val documentListener = AutocompleteDocumentListener(editorManager, editor)
        editor.document.addDocumentListener(documentListener)

        disposables[editor] = {
            editor.caretModel.removeCaretListener(caretListener)
            connection?.disconnect()
            editor.document.removeDocumentListener(documentListener)
        }
    }

    override fun editorReleased(event: EditorFactoryEvent) {
        val editor = event.editor
        val disposable = disposables[editor]
        disposable?.invoke()
        disposables.remove(editor)
    }
}
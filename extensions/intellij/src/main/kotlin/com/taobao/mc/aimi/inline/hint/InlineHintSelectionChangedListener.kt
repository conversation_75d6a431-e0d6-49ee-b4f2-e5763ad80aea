package com.taobao.mc.aimi.inline.hint

import com.intellij.codeInsight.hint.HintManager
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.VisualPosition
import com.intellij.openapi.editor.event.EditorMouseEvent
import com.intellij.openapi.editor.event.EditorMouseMotionListener
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.ui.LightweightHint
import com.taobao.mc.aimi.ext.editor.EditorUtils
import com.taobao.mc.aimi.ext.listeners.RemoveAllTooltipsListener
import com.taobao.mc.aimi.ext.utils.Debouncer
import com.taobao.mc.aimi.inline.utils.HintUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import java.util.concurrent.ConcurrentHashMap

/**
 * Listener for selection changes in the editor to show/hide inline hints
 */
class InlineHintSelectionChangedListener(private val coroutineScope: CoroutineScope) : SelectionListener, RemoveAllTooltipsListener, Disposable {

    companion object {
        private val hintMap = ConcurrentHashMap<Editor, LightweightHint>()
    }

    private val debouncer = Debouncer(200, coroutineScope)


    /**
     * Handle selection change events
     */
    override fun selectionChanged(e: SelectionEvent) {
        val editor = e.editor

        if (!isValid(editor)) {
            return
        }

        // val showInlineChatHint = getSettingsState().isShowInlineChatHint
        // if (!showInlineChatHint) {
        //     return
        // }

        // Schedule hint update with a delay
        debouncer.debounce {
            ApplicationManager.getApplication().invokeAndWait {
                showHintForEditor(editor)
            }
        }
    }

    override fun removeAllTooltips() {
        dispose()
    }

    /**
     * Show hint for the given editor
     */
    private fun showHintForEditor(editor: Editor) {
        if (!isValid(editor)) {
            return
        }

        // Hide existing hint if present
        val existingHint = hintMap[editor]
        if (existingHint != null) {
            existingHint.hide()
            hintMap.remove(editor)
        }

        // Create and show new hint
        val hint = AIMIFloatingToolbar.createInlineChatHint(editor)
        val hintPosition = HintUtils.getHintPosition(hint, editor)

        val flags = HintManager.HIDE_BY_ESCAPE or HintManager.UPDATE_BY_SCROLLING or HintManager.HIDE_BY_TEXT_CHANGE // or HintManager.HIDE_BY_ANY_KEY
        HintUtils.showEditorHint(
            hint = hint,
            editor = editor,
            constraint = HintManager.RIGHT_UNDER,
            flags = flags,
            timeout = 0,
            reviveOnEditorChange = false,
            point = hintPosition
        )

        // Store the hint for later cleanup
        // editor.addEditorMouseMotionListener(MouseMotionListener(editor), this)
        hintMap[editor] = hint
    }

    private fun showIfHidden(editor: Editor) {
        val hint = hintMap[editor]
        if (hint?.isVisible == true) return
        showHintForEditor(editor)
    }

    /**
     * Check if the editor is valid for showing hints
     */
    private fun isValid(editor: Editor?): Boolean {
        return editor != null &&
                editor.selectionModel.hasSelection() &&
                EditorUtils.isMainEditor(editor)
    }

    /**
     * Dispose resources and hide all hints
     */
    override fun dispose() {
        coroutineScope.cancel()
        val values = hintMap.values
        if (values.isNotEmpty()) {
            values.forEach { hint -> hint.hide() }
        }

        hintMap.clear()
    }

    private inner class MouseMotionListener(val editor: Editor) : EditorMouseMotionListener {
        override fun mouseMoved(event: EditorMouseEvent) {
            val visualPosition = event.visualPosition
            val hoverSelected = editor.caretModel.allCarets.any { visualPosition.isInsideSelection(it) }
            if (hoverSelected) {
                showIfHidden(editor)
            }
        }

        private fun VisualPosition.isInsideSelection(caret: Caret): Boolean {
            val beforeSelectionEnd = caret.selectionEndPosition.after(this)
            val afterSelectionStart = this.after(caret.selectionStartPosition)
            return beforeSelectionEnd && afterSelectionStart
        }
    }
}
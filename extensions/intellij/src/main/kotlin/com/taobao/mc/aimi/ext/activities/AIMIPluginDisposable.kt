package com.taobao.mc.aimi.ext.activities

import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager

/**
 * The service is a parent disposable that represents the entire plugin lifecycle
 * and is intended to be used instead of the project/application as a parent disposable,
 * ensures that disposables registered using it as parents will be processed when the plugin is unloaded to avoid memory leaks.
 *
 * <AUTHOR>
 */
@Service(Service.Level.APP, Service.Level.PROJECT)
class AIMIPluginDisposable : Disposable {
    private val logger = LoggerManager.getLogger(AIMIPluginDisposable::class.java)

    override fun dispose() {
        logger.info("AIMIPluginDisposable is being disposed")
        // 这里会自动清理所有注册到此 Disposable 的子资源
    }

    companion object {

        fun getInstance(): AIMIPluginDisposable {
            return ApplicationManager.getApplication().getService(AIMIPluginDisposable::class.java)
        }

        fun getInstance(project: Project): AIMIPluginDisposable {
            return project.getService(AIMIPluginDisposable::class.java)
        }

    }
}
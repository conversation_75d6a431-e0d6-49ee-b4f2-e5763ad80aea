package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.ui.Messages
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.launch
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.*

/**
 * RipGrep搜索Action - 调用IntelliJIDE的getFileResults和getSearchResults方法
 */
class RipGrepSearchAction : AnAction("RipGrep搜索") {
    private val logger = LoggerManager.getLogger(RipGrepSearchAction::class.java)

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return
        val continuePluginService = project.service<AIMIPluginService>()

        logger.info("执行RipGrep搜索Action")

        // 显示自定义搜索对话框
        val dialog = RipGrepSearchDialog(project)
        if (dialog.showAndGet()) {
            val searchText = dialog.getSearchText()
            val isFileSearch = dialog.isFileSearch()

            if (searchText.isBlank()) {
                logger.info("搜索内容为空")
                return
            }

            if (isFileSearch) {
                performFileSearch(project, continuePluginService, searchText)
            } else {
                performContentSearch(project, continuePluginService, searchText)
            }
        } else {
            logger.info("用户取消搜索")
        }
    }

    /**
     * 自定义搜索对话框
     */
    private class RipGrepSearchDialog(project: com.intellij.openapi.project.Project) : DialogWrapper(project) {
        private val searchTextField = JTextField()
        private val fileSearchCheckBox = JCheckBox("文件搜索 (例如: *.kt, *.java)", true)
        private val contentSearchCheckBox = JCheckBox("内容搜索", false)

        init {
            title = "RipGrep 搜索"
            init()

            // 设置单选行为
            fileSearchCheckBox.addActionListener {
                if (fileSearchCheckBox.isSelected) {
                    contentSearchCheckBox.isSelected = false
                }
            }

            contentSearchCheckBox.addActionListener {
                if (contentSearchCheckBox.isSelected) {
                    fileSearchCheckBox.isSelected = false
                }
            }
        }

        override fun createCenterPanel(): JComponent {
            val panel = JPanel(BorderLayout())
            panel.preferredSize = Dimension(400, 120)

            // 输入框区域
            val inputPanel = JPanel(BorderLayout())
            inputPanel.add(JLabel("搜索内容:"), BorderLayout.NORTH)
            searchTextField.preferredSize = Dimension(350, 25)
            inputPanel.add(searchTextField, BorderLayout.CENTER)

            // 选项区域
            val optionsPanel = JPanel()
            optionsPanel.layout = BoxLayout(optionsPanel, BoxLayout.Y_AXIS)
            optionsPanel.border = BorderFactory.createTitledBorder("搜索类型")
            optionsPanel.add(fileSearchCheckBox)
            optionsPanel.add(contentSearchCheckBox)

            panel.add(inputPanel, BorderLayout.NORTH)
            panel.add(optionsPanel, BorderLayout.CENTER)

            // 设置焦点到输入框
            SwingUtilities.invokeLater {
                searchTextField.requestFocusInWindow()
            }

            return panel
        }

        override fun doOKAction() {
            // 确保至少选择了一种搜索类型
            if (!fileSearchCheckBox.isSelected && !contentSearchCheckBox.isSelected) {
                Messages.showWarningDialog(
                    "请选择至少一种搜索类型",
                    "警告"
                )
                return
            }
            super.doOKAction()
        }

        fun getSearchText(): String = searchTextField.text.trim()

        fun isFileSearch(): Boolean = fileSearchCheckBox.isSelected
    }

    /**
     * 执行文件搜索
     */
    private fun performFileSearch(project: com.intellij.openapi.project.Project, continuePluginService: AIMIPluginService, pattern: String) {
        // 使用协程执行搜索
        continuePluginService.coroutineScope.launch {
            try {
                val intellijIDE = continuePluginService.ideProtocolClient?.ide ?: return@launch
                val results = intellijIDE.getFileResults(pattern)

                logger.info("文件搜索完成，找到 ${results.size} 个文件")

                // 在EDT中显示结果
                com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater {
                    val resultText = if (results.isEmpty()) {
                        "未找到匹配的文件"
                    } else {
                        "找到 ${results.size} 个文件:\n" + results.take(20).joinToString("\n") +
                                if (results.size > 20) "\n... 还有 ${results.size - 20} 个文件" else ""
                    }
                    logger.info("搜索结果部分信息: $resultText")

                    Messages.showMessageDialog(
                        project,
                        resultText,
                        "文件搜索结果",
                        Messages.getInformationIcon()
                    )
                }
            } catch (e: Exception) {
                logger.warn("文件搜索失败", e)
                com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater {
                    Messages.showErrorDialog(
                        project,
                        "搜索失败: ${e.message}",
                        "错误"
                    )
                }
            }
        }
    }

    /**
     * 执行内容搜索
     */
    private fun performContentSearch(project: com.intellij.openapi.project.Project, continuePluginService: AIMIPluginService, query: String) {
        // 使用协程执行搜索
        continuePluginService.coroutineScope.launch {
            try {
                val intellijIDE = continuePluginService.ideProtocolClient?.ide ?: return@launch
                val results = intellijIDE.getSearchResults(query)

                logger.info("内容搜索完成")

                // 在EDT中显示结果
                com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater {
                    val resultText = if (results.isBlank()) {
                        "未找到匹配的内容"
                    } else {
                        // 限制显示长度，避免对话框过大
                        if (results.length > 2000) {
                            results.take(2000) + "\n... (结果过长，已截断)"
                        } else {
                            results
                        }
                    }
                    logger.info("搜索结果部分信息: $resultText")

                    Messages.showMessageDialog(
                        project,
                        resultText,
                        "内容搜索结果",
                        Messages.getInformationIcon()
                    )
                }
            } catch (e: Exception) {
                logger.warn("内容搜索失败", e)
                com.intellij.openapi.application.ApplicationManager.getApplication().invokeLater {
                    Messages.showErrorDialog(
                        project,
                        "搜索失败: ${e.message}",
                        "错误"
                    )
                }
            }
        }
    }

    override fun update(e: AnActionEvent) {
        // 只有在有项目的情况下才启用此 Action
        e.presentation.isEnabled = e.project != null
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}
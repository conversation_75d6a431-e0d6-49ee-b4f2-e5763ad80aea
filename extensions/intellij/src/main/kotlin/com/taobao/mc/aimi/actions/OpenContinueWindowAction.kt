package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.taobao.mc.aimi.ext.actions.aimiService
import com.taobao.mc.aimi.types.WindowType
import kotlinx.coroutines.launch

/**
 * 打开Continue窗口的Action
 */
class OpenContinueWindowAction : AnAction("Continue") {
    override fun actionPerformed(e: AnActionEvent) {
        val aimiService = e.project?.aimiService ?: return
        aimiService.coroutineScope.launch {
            aimiService.openNonResidentWindow(WindowType.Continue)
        }
    }
}

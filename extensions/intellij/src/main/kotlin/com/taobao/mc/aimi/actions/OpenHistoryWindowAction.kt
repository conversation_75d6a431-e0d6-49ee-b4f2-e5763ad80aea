package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.taobao.mc.aimi.ext.actions.getAIMIPluginService
import com.taobao.mc.aimi.types.WindowType
import kotlinx.coroutines.launch

/**
 * 打开历史会话窗口的Action
 */
class OpenHistoryWindowAction: AnAction("历史会话") {
    override fun actionPerformed(e: AnActionEvent) {
        val continuePluginService = getAIMIPluginService(e.project) ?: return
        continuePluginService.coroutineScope.launch {
            continuePluginService.openNonResidentWindow(WindowType.History)
        }
    }
}
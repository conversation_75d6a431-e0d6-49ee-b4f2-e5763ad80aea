package com.taobao.mc.aimi.util

import com.taobao.mc.aimi.settings.AIMISettingService

/**
 * URL管理器，根据环境设置生成对应的URL
 */
object URLManager {

    // 环境host配置
    private val HOSTS = mapOf(
        "release" to "https://aimi.alibaba-inc.com",
        "debug" to "https://pre-aimi.alibaba-inc.com"
    )

    // URL路径配置
    private val URL_PATHS = mapOf(
        "GUI_URL" to "http://localhost:5173/jetbrains_index.html",
        "AIMI_URL" to "/#/ide/dashboard",
        "HISTORY_URL" to "/#/ide/history",
        "INTEGRATION_URL" to "/#/ide/workflow",
        "API_URL" to "/#/ide/api"
    )

    /**
     * 根据当前环境设置获取URL
     */
    fun getUrl(urlKey: String): String {
        val environment = AIMISettingService.instance.state.environment

        // GUI_URL 特殊处理，不依赖环境
        if (urlKey == "GUI_URL") {
            return URL_PATHS[urlKey] ?: ""
        }

        val host = HOSTS[environment] ?: HOSTS["release"]!!
        val path = URL_PATHS[urlKey]

        return if (path != null) {
            host + path
        } else {
            System.getenv(urlKey) ?: getDefaultUrl()
        }
    }

    /**
     * 获取默认URL
     */
    private fun getDefaultUrl(): String {
        val environment = AIMISettingService.instance.state.environment
        val host = HOSTS[environment] ?: HOSTS["release"]!!
        return "$host/#/ide/dashboard"
    }

    /**
     * 获取当前环境设置
     */
    fun getCurrentEnvironment(): String {
        return AIMISettingService.instance.state.environment
    }

    /**
     * 检查环境是否发生变化
     */
    fun hasEnvironmentChanged(lastEnvironment: String?): Boolean {
        val currentEnvironment = AIMISettingService.instance.state.environment
        return lastEnvironment != currentEnvironment
    }

    fun getLogUploadURL(): String {
        val host = HOSTS[AIMISettingService.instance.state.environment] ?: HOSTS["release"]!!
        return "$host/ai/api/v1/ide/log/record"
    }
}
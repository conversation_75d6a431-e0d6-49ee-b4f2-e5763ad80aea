package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.ui.Messages
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.services.UIFreezeDetectionService

/**
 * 测试UI冻结的Action（仅用于开发和测试）
 * 故意阻塞EDT线程来触发UI冻结检测
 */
class TestUIFreezeAction : AnAction("测试UI冻结") {

    private val logger = LoggerManager.getLogger(javaClass)

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return

        // 询问用户要冻结多长时间
        val input = Messages.showInputDialog(
            "请输入要冻结的时间（毫秒）:",
            "测试UI冻结",
            Messages.getQuestionIcon()
        )

        val freezeTime = input?.toLongOrNull()
        if (freezeTime == null || freezeTime <= 0) {
            Messages.showWarningDialog(
                project,
                "请输入有效的时间（正整数）",
                "输入错误"
            )
            return
        }

        logger.warn("即将开始UI冻结测试，冻结时间: ${freezeTime}ms")

        // 确认对话框
        val confirmed = Messages.showYesNoDialog(
            project,
            "确定要冻结UI ${freezeTime}ms吗？\n这将导致IDE暂时无响应。",
            "确认测试",
            Messages.getWarningIcon()
        )

        if (confirmed == Messages.YES) {
            // 在EDT线程中执行阻塞操作
            ApplicationManager.getApplication().invokeLater {
                logger.warn("开始UI冻结测试")

                try {
                    // 阻塞EDT线程
                    Thread.sleep(freezeTime)

                    logger.warn("UI冻结测试结束")

                    // 显示完成消息
                    val confirmed = Messages.showYesNoDialog(
                        project,
                        "UI冻结测试完成！\n冻结时间: ${freezeTime}ms\n请查看日志以确认检测是否正常工作。",
                        "测试完成",
                        Messages.getWarningIcon(),
                    )
                    if (confirmed == Messages.YES) {
                        // 显示统计信息
                        val uiFreezeDetectionService = project.service<UIFreezeDetectionService>()
                        val statistics = uiFreezeDetectionService.getDetectionStatistics()
                        Messages.showInfoMessage(
                            project,
                            statistics,
                            "UI冻结检测统计",
                        )
                    }
                } catch (e: InterruptedException) {
                    logger.warn("UI冻结测试被中断", e)
                    Thread.currentThread().interrupt()
                } catch (e: Exception) {
                    logger.warn("UI冻结测试异常", e)
                    Messages.showErrorDialog(
                        project,
                        "测试过程中发生异常: ${e.message}",
                        "测试错误"
                    )
                }
            }
        }
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}

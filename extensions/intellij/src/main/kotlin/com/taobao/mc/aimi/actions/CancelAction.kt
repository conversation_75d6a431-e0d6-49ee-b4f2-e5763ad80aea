package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.util.application
import com.taobao.mc.aimi.ext.listeners.RemoveAllTooltipsListener

class CancelAction : AnAction() {
    override fun actionPerformed(e: AnActionEvent) {
        // 如果存在 tooltips, 就清除
        application.messageBus.syncPublisher(RemoveAllTooltipsListener.TOPIC).removeAllTooltips()
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}
package com.taobao.mc.aimi.logger

import com.intellij.openapi.project.Project
import com.intellij.openapi.util.text.StringUtil
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * UI冻结日志记录器
 * 负责记录UI冻结事件的详细信息，包括堆栈跟踪、时间戳和场景信息
 */
class UIFreezeLogger(private val project: Project) {
    private val logger = LoggerManager.getLogger(javaClass)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    // 存储最近的冻结事件，避免重复记录
    private val recentFreezeEvents = ConcurrentLinkedQueue<FreezeEvent>()
    private val maxRecentEvents = 10

    data class FreezeEvent(
        val timestamp: Long,
        val duration: Long,
        val stackTrace: String,
        val scenario: String
    )

    /**
     * 记录UI冻结事件
     * @param duration 冻结持续时间（毫秒）
     * @param scenario 冻结发生的场景描述
     * @param stackTrace 可选的堆栈跟踪信息
     */
    fun logUIFreeze(duration: Long, scenario: String, stackTrace: String? = null) {
        val timestamp = System.currentTimeMillis()
        val currentStackTrace = stackTrace ?: getCurrentStackTrace()

        val freezeEvent = FreezeEvent(timestamp, duration, currentStackTrace, scenario)

        // 检查是否是重复事件
        if (isDuplicateEvent(freezeEvent)) {
            logger.debug("跳过重复的UI冻结事件: $scenario")
            return
        }

        // 添加到最近事件列表
        addToRecentEvents(freezeEvent)

        // 记录详细日志
        logDetailedFreezeInfo(freezeEvent)

        // 如果冻结时间超过严重阈值，记录为错误
        if (duration > SEVERE_FREEZE_THRESHOLD) {
            logger.warn("严重UI冻结检测到: 持续时间=${duration}ms, 场景=$scenario")
        } else {
            logger.warn("UI冻结检测到: 持续时间=${duration}ms, 场景=$scenario")
        }
    }

    /**
     * 记录详细的冻结信息
     */
    private fun logDetailedFreezeInfo(event: FreezeEvent) {
        val logMessage = buildString {
            appendLine("=== UI冻结事件详情 ===")
            appendLine("时间: ${dateFormat.format(Date(event.timestamp))}")
            appendLine("持续时间: ${event.duration}ms")
            appendLine("场景: ${event.scenario}")
            appendLine("项目: ${project.name}")
            appendLine("堆栈跟踪:")
            appendLine(event.stackTrace)
            appendLine("========================")
        }

        logger.info(logMessage)
    }

    /**
     * 获取当前线程的堆栈跟踪
     */
    private fun getCurrentStackTrace(): String {
        val stringWriter = StringWriter()
        val printWriter = PrintWriter(stringWriter)

        // 获取EDT线程的堆栈跟踪
        val edtThread = Thread.getAllStackTraces().entries
            .find { it.key.name.contains("AWT-EventQueue") }

        if (edtThread != null) {
            edtThread.value.forEach { stackElement ->
                printWriter.println("\tat $stackElement")
            }
        } else {
            // 如果找不到EDT线程，使用当前线程的堆栈
            Thread.currentThread().stackTrace.forEach { stackElement ->
                printWriter.println("\tat $stackElement")
            }
        }

        return stringWriter.toString()
    }

    /**
     * 检查是否是重复的冻结事件
     */
    private fun isDuplicateEvent(newEvent: FreezeEvent): Boolean {
        val currentTime = System.currentTimeMillis()

        return recentFreezeEvents.any { event ->
            // 如果在5秒内有相同场景的事件，认为是重复
            currentTime - event.timestamp < 5000 &&
                    event.scenario == newEvent.scenario &&
                    StringUtil.equals(event.stackTrace, newEvent.stackTrace)
        }
    }

    /**
     * 添加到最近事件列表
     */
    private fun addToRecentEvents(event: FreezeEvent) {
        recentFreezeEvents.offer(event)

        // 保持队列大小
        while (recentFreezeEvents.size > maxRecentEvents) {
            recentFreezeEvents.poll()
        }

        // 清理过期事件（超过1分钟）
        val currentTime = System.currentTimeMillis()
        recentFreezeEvents.removeIf { currentTime - it.timestamp > 60000 }
    }

    /**
     * 获取最近的冻结事件统计
     */
    fun getRecentFreezeStatistics(): String {
        val currentTime = System.currentTimeMillis()
        val recentEvents = recentFreezeEvents.filter { currentTime - it.timestamp < 300000 } // 5分钟内

        if (recentEvents.isEmpty()) {
            return "最近5分钟内无UI冻结事件"
        }

        val totalEvents = recentEvents.size
        val avgDuration = recentEvents.map { it.duration }.average()
        val maxDuration = recentEvents.maxOfOrNull { it.duration } ?: 0
        val scenarios = recentEvents.groupBy { it.scenario }.mapValues { it.value.size }

        return buildString {
            appendLine("=== 最近5分钟UI冻结统计 ===")
            appendLine("总事件数: $totalEvents")
            appendLine("平均持续时间: ${String.format("%.1f", avgDuration)}ms")
            appendLine("最长持续时间: ${maxDuration}ms")
            appendLine("场景分布:")
            scenarios.forEach { (scenario, count) ->
                appendLine("  - $scenario: $count 次")
            }
            appendLine("==========================")
        }
    }

    companion object {
        private const val SEVERE_FREEZE_THRESHOLD = 5000L // 5秒认为是严重冻结
    }
}

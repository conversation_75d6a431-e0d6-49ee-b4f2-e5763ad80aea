package com.taobao.mc.aimi.protocol

import com.google.gson.Gson
import com.intellij.openapi.components.service
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.ext.*
import com.taobao.mc.aimi.ext.core.IntelliJIDE
import com.taobao.mc.aimi.ext.editor.DiffStreamService
import com.taobao.mc.aimi.ext.editor.EditorUtils
import com.taobao.mc.aimi.ext.editor.RangeInFileWithContents
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.ext.utils.uuid
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.protocol.handlers.MessageHandlerFactory
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.types.MessageTypes.ToWebview
import kotlinx.coroutines.*


class IdeProtocolClient(
    private val continuePluginService: AIMIPluginService,
    private val coroutineScope: CoroutineScope,
    private val project: Project
) : DumbAware {
    val ide: IDE = IntelliJIDE(project, continuePluginService)
    private val diffStreamService = project.service<DiffStreamService>()
    private val logger = LoggerManager.getLogger(javaClass)

    // 消息处理器工厂
    private val messageHandlerFactory = MessageHandlerFactory(
        project,
        ide,
        continuePluginService,
        diffStreamService
    )


    /**
     * Create a dispatcher with limited parallelism to prevent UI freezing.
     * Note that there are 64 total threads available to the IDE.
     *
     * See this thread for details: https://github.com/continuedev/continue/issues/4098#issuecomment-2854865310
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    private val limitedDispatcher = Dispatchers.IO.limitedParallelism(4)

    init {}

    fun handleMessage(msg: String, respond: (Any?) -> Unit) {
        coroutineScope.launch(limitedDispatcher) {
            try {
                val message = Gson().fromJson(msg, Message::class.java)
                val messageType = MessageTypes.fromIDE(message.messageType) ?: run {
                    logger.warn("Unknown message type: ${message.messageType}")
                    respond(null)
                    return@launch
                }
                val dataElement = message.data
                val messageId = message.messageId

                // 使用策略模式处理消息，统一异常保护
                val handler = messageHandlerFactory.getHandler(messageType) ?: run {
                    logger.warn("No handler found for message type: ${messageType.type}")
                    respond(null)
                    return@launch
                }
                try {
                    // 统一的异常保护：所有 handler 的异常都在这里捕获
                    handler.handle(messageId, dataElement, respond)
                } catch (timeout: TimeoutCancellationException) {
                    logger.warn("Timeout in handler for message type ${messageType.type}", timeout)
                    // ide.showToast(ToastType.ERROR, "Timeout in handling message of type ${messageType.type}")
                    respond(null)
                } catch (handlerError: Exception) {
                    logger.warn("Error in handler for message type ${messageType.type}", handlerError)
                    ide.showToast(ToastType.ERROR, "Error handling message of type ${messageType.type}: ${handlerError.message}")
                    respond(null)
                }
            } catch (parseError: Exception) {
                logger.warn("Error parsing message: $msg", parseError)
                ide.showToast(ToastType.ERROR, "Error parsing message: ${parseError.message}")
                respond(null)
            }
        }
    }

    fun sendHighlightedCode(shouldRun: Boolean = false, newSession: Boolean = false, prompt: String? = null) {
        val editor = EditorUtils.getEditor(project)
        val filepath = editor?.editor?.virtualFile?.toUriOrNull()
        val rif = editor?.getHighlightedRIF() ?: run {
            if (filepath.isNullOrEmpty()) return
            val document = editor.editor.document
            RangeInFileWithContents(
                filepath = filepath,
                range = null,
                contents = document.text,
                document = document,
            )
        }

        continuePluginService.sendToWebview(
            ToWebview.HighlightedCode,
            HighlightedCodePayload(
                rangeInFileWithContents = rif,
                shouldRun = shouldRun,
                prompt = prompt,
                newSession = newSession,
            )
        )
    }


    fun sendAcceptRejectDiff(accepted: Boolean, stepIndex: Int) {
        continuePluginService.sendToWebview(ToWebview.AcceptRejectDiff, AcceptRejectDiff(accepted, stepIndex), uuid())
    }


    fun deleteAtIndex(index: Int) {
        continuePluginService.sendToWebview(ToWebview.DeleteAtIndex, DeleteAtIndex(index), uuid())
    }
}
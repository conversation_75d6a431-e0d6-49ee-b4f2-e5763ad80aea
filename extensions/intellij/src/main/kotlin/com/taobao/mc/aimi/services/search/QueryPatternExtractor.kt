package com.taobao.mc.aimi.services.search

import com.taobao.mc.aimi.logger.LoggerManager

/**
 * 查询模式提取工具类
 * 负责从搜索模式中提取对应的方法名或符号
 */
object QueryPatternExtractor {
    private val logger = LoggerManager.getLogger(javaClass)

    /**
     * 从搜索模式中提取查询字符串
     */
    fun extractQueryFromPattern(searchPattern: String, cursorIndex: Int): String {
        // 如果搜索模式为空或光标位置无效，返回空字符串
        if (searchPattern.isEmpty() || cursorIndex < 0 || cursorIndex > searchPattern.length) {
            return ""
        }

        // 获取光标前的部分用于分析
        val prefixPart = searchPattern.substring(0, cursorIndex)

        // 查找光标位置的上下文
        return when {
            // 情况1: 光标在括号内 - 向前查找最近的方法调用
            isInsideParentheses(searchPattern, cursorIndex) -> {
                extractMethodFromParentheses(prefixPart)
            }

            // 情况2: 光标在方法名后面（可能紧跟括号）
            prefixPart.endsWith("()") || prefixPart.endsWith("(") -> {
                extractMethodFromSuffix(prefixPart)
            }

            // 情况3: 光标在链式调用中
            prefixPart.contains(".") -> {
                extractMethodFromChain(prefixPart)
            }

            // 情况4: 简单的标识符
            else -> {
                extractSimpleIdentifier(prefixPart)
            }
        }
    }

    /**
     * 检查光标是否在括号内
     */
    private fun isInsideParentheses(text: String, cursorIndex: Int): Boolean {
        var openParens = 0
        for (i in 0 until cursorIndex) {
            when (text[i]) {
                '(' -> openParens++
                ')' -> openParens--
            }
        }
        return openParens > 0
    }

    /**
     * 从括号内位置向前查找方法名
     */
    private fun extractMethodFromParentheses(prefix: String): String {
        logger.debug("extractMethodFromParentheses: prefix='$prefix'")

        // 从后往前找到第一个未匹配的开括号
        var parenLevel = 0

        for (i in prefix.length - 1 downTo 0) {
            when (prefix[i]) {
                ')' -> parenLevel++
                '(' -> {
                    if (parenLevel == 0) {
                        // 找到了第一个未匹配的开括号
                        val beforeParen = prefix.substring(0, i)
                        logger.debug("Found unmatched '(' at position $i, beforeParen='$beforeParen'")
                        return extractMethodFromChain(beforeParen)
                    } else {
                        parenLevel--
                        logger.debug("Found matched '(' at position $i, parenLevel=$parenLevel")
                    }
                }
            }
        }

        logger.debug("No unmatched paren found")
        return ""
    }

    /**
     * 从方法调用后缀提取方法名
     */
    private fun extractMethodFromSuffix(prefix: String): String {
        val cleanPrefix = prefix.removeSuffix("()").removeSuffix("(")
        return extractMethodFromChain(cleanPrefix)
    }

    /**
     * 从链式调用中提取最后的方法名
     */
    private fun extractMethodFromChain(text: String): String {
        if (text.isEmpty()) return ""

        // 查找最后一个点
        val lastDot = text.lastIndexOf('.')
        if (lastDot == -1) {
            // 没有点，直接提取标识符
            return extractSimpleIdentifier(text)
        }

        // 获取点后面的部分
        val afterDot = text.substring(lastDot + 1)
        return extractSimpleIdentifier(afterDot)
    }

    /**
     * 提取简单标识符（只包含字母、数字、下划线）
     */
    private fun extractSimpleIdentifier(text: String): String {
        if (text.isEmpty()) return ""

        // 从末尾开始，找到最长的有效标识符
        val identifierRegex = Regex("[a-zA-Z_][a-zA-Z0-9_]*")
        val matches = identifierRegex.findAll(text)

        // 返回最后一个匹配的标识符
        return matches.lastOrNull()?.value?.trim() ?: ""
    }
}
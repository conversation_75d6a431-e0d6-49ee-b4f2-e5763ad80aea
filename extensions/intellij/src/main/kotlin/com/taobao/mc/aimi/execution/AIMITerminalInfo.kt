package com.taobao.mc.aimi.execution

import com.intellij.execution.ExecutionManager
import com.intellij.execution.OutputListener
import com.intellij.execution.process.ProcessHandler
import com.intellij.execution.ui.RunContentManager
import com.intellij.openapi.project.Project
import com.jetbrains.rd.generator.nova.GenerationSpec.Companion.nullIfEmpty

/**
 * ProcessTerminalInfo - 进程终端信息
 *
 * 继承自OutputListener，用于管理进程的终端信息，包括命令、工作目录、创建时间等
 */
class AIMITerminalInfo(
    val terminalId: Long,
    val command: String,
    val workingDirectory: String?
) : OutputListener() {

    val createdTime: Long = System.currentTimeMillis()
    private var processHandler: ProcessHandler? = null

    /**
     * 进程的当前状态
     */
    enum class ProcessStatus {
        UNKNOWN,
        TERMINATED,
        TERMINATING,
        RUNNING
    }

    /**
     * 附加到进程处理器
     * @param processHandler 进程处理器
     * @param project 项目实例
     */
    fun attach(processHandler: ProcessHandler, project: Project) {
        this.processHandler = processHandler
        processHandler.addProcessListener(this)
        PROCESS_TERMINAL_KEY.set(processHandler, this)
    }

    fun waitFor(timeoutInMilliseconds: Long): Boolean {
        val processHandler = processHandler ?: return false
        return if (timeoutInMilliseconds <= 0) {
            processHandler.waitFor()
        } else {
            processHandler.waitFor(timeoutInMilliseconds)
        }
    }

    /**
     * 获取进程状态
     */
    fun getStatus(): ProcessStatus = when {
        processHandler == null -> ProcessStatus.UNKNOWN
        processHandler!!.isProcessTerminated -> ProcessStatus.TERMINATED
        processHandler!!.isProcessTerminating -> ProcessStatus.TERMINATING
        else -> ProcessStatus.RUNNING
    }

    /**
     * 获取退出码，如果进程未结束则返回null
     */
    val exitCode: Int?
        get() = processHandler?.exitCode

    /**
     * 判断进程是否正在运行
     */
    fun isRunning(): Boolean = processHandler?.let { !it.isProcessTerminated } ?: false

    fun getTerminalOutput(): String = if (getStatus() == ProcessStatus.TERMINATED) {
        (if (exitCode == 0) output.stdout else output.stderr) ?: ""
    } else {
        output.stdout.nullIfEmpty() ?: output.stderr.nullIfEmpty() ?: ""
    }

    override fun toString(): String = buildString {
        append("ProcessTerminalInfo{")
        append("createdTime=$createdTime, ")
        append("terminalId=$terminalId, ")
        append("command='$command', ")
        append("workingDirectory='$workingDirectory', ")
        append("status='${getStatus()}', ")
        append("exitCode=$exitCode")
        append("}")
    }

    companion object {
        fun fromConfiguration(configuration: AIMIRunConfiguration): AIMITerminalInfo {
            return AIMITerminalInfo(
                terminalId = configuration.terminalId,
                command = configuration.command,
                workingDirectory = configuration.workingDirectory
            )
        }

        fun findActiveTerminal(project: Project): AIMITerminalInfo? {
            val runManager = RunContentManager.getInstance(project)
            // 首先尝试从当前选中的内容获取终端信息
            runManager.selectedContent?.processHandler?.getUserData(PROCESS_TERMINAL_KEY)?.let {
                return it
            }

            // 如果没有找到，则从所有描述符中查找第一个有效的终端信息
            return runManager.allDescriptors
                .firstNotNullOfOrNull { descriptor ->
                    descriptor?.processHandler?.getUserData(PROCESS_TERMINAL_KEY)
                }
        }

        fun findTerminalInfo(project: Project, id: Long): AIMITerminalInfo? {
            val runManager = RunContentManager.getInstance(project)

            // 首先尝试从当前选中的内容获取终端信息
            runManager.selectedContent
                ?.processHandler
                ?.getUserData(PROCESS_TERMINAL_KEY)
                ?.takeIf { it.terminalId == id }
                ?.let { return it }

            // 从所有描述符中查找
            runManager.allDescriptors
                .firstNotNullOfOrNull { descriptor ->
                    descriptor?.processHandler?.getUserData(PROCESS_TERMINAL_KEY)?.takeIf { it.terminalId == id }
                }?.let { return it }

            // 最后从正在运行的进程中查找
            return ExecutionManager.getInstance(project).getRunningProcesses()
                .firstNotNullOfOrNull { handler ->
                    PROCESS_TERMINAL_KEY.get(handler)?.takeIf { it.terminalId == id }
                }
        }
    }
}
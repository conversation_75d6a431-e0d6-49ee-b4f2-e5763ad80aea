package com.taobao.mc.aimi.inline.utils

import com.intellij.codeInsight.hint.HintManager
import com.intellij.codeInsight.hint.HintManager.PositionFlags
import com.intellij.codeInsight.hint.HintManagerImpl
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.VisualPosition
import com.intellij.openapi.util.ProperTextRange
import com.intellij.openapi.util.TextRange
import com.intellij.ui.LightweightHint
import java.awt.Point
import kotlin.math.max
import kotlin.math.min


/**
 * Utility class for handling inline hints in the editor
 */
object HintUtils {

    private const val HORIZONTAL_GAP = 10
    private const val MARGIN = 20

    /**
     * Calculate the visible range of the editor
     */
    @JvmStatic
    fun calculateVisibleRange(editor: Editor): ProperTextRange {
        return ReadAction.compute<ProperTextRange, RuntimeException> {
            val rect = editor.scrollingModel.visibleArea
            val startPosition = editor.xyToLogicalPosition(Point(rect.x, rect.y))
            val visibleStart = editor.logicalPositionToOffset(startPosition)

            val endPosition = editor.xyToLogicalPosition(
                Point(rect.x + rect.width, rect.y + rect.height)
            )
            val visibleEnd = editor.logicalPositionToOffset(
                LogicalPosition(endPosition.line + 1, 0)
            )

            ProperTextRange(visibleStart, max(visibleEnd, visibleStart))
        }
    }

    /**
     * Get the position for displaying a hint
     */
    @JvmStatic
    fun getHintPosition(hint: LightweightHint, editor: Editor): Point {
        val calculateVisibleRange = calculateVisibleRange(editor)
        val selectionEnd = editor.selectionModel.selectionEnd
        val selectionStart = editor.selectionModel.selectionStart
        val isOneLineSelection = isOneLineSelection(editor)
        val isBelow = shouldBeUnderSelection(selectionEnd, editor)
        val isCursorAtEnd = editor.caretModel.offset == selectionEnd

        val areEdgesOutsideOfVisibleArea = !calculateVisibleRange.contains(selectionStart) &&
                !calculateVisibleRange.contains(selectionEnd)

        val offsetForHint = when {
            isOneLineSelection -> selectionStart
            areEdgesOutsideOfVisibleArea -> {
                val caretOffset = editor.caretModel.offset
                val line = getLineByVisualStart(editor, caretOffset, true)
                getOffsetForLine(editor, line)
            }

            isBelow -> {
                val line = getLineByVisualStart(editor, selectionEnd, true)
                getOffsetForLine(editor, line)
            }

            else -> {
                val line = getLineByVisualStart(editor, selectionStart, false)
                getOffsetForLine(editor, line)
            }
        }

        val visualPosition = editor.offsetToVisualPosition(offsetForHint)
        // 获取组件显示位置, 默认在光标后面
        val hintPoint = HintManagerImpl.getHintPosition(hint, editor, visualPosition, HintManager.DEFAULT)

        val dy = if (isBelow) {
            editor.lineHeight + 2
        } else {
            -(hint.component.preferredSize.height + 2)
        }

        val caretXY = editor.visualPositionToXY(visualPosition)
        val dx = if (isBelow || isCursorAtEnd) {
            val endLine = editor.document.getLineNumber(selectionEnd)
            val lineStartOffset = getLineFirstNonWhitespaceOffset(editor.document, endLine)
            val position = editor.offsetToVisualPosition(lineStartOffset)
            maxOf(editor.visualPositionToXY(position).x + 250, caretXY.x + 16)
        } else {
            caretXY.x + 16
        }

        hintPoint.translate(dx, dy)
        return hintPoint
    }

    /**
     * Check if the selection is on one line
     */
    private fun isOneLineSelection(editor: Editor): Boolean {
        val document = editor.document
        val selectionModel = editor.selectionModel
        val startLine = document.getLineNumber(selectionModel.selectionStart)
        val endLine = document.getLineNumber(selectionModel.selectionEnd)
        return startLine == endLine
    }

    /**
     * Check if hint should be displayed under the selection
     */
    private fun shouldBeUnderSelection(selectionEnd: Int, editor: Editor): Boolean {
        val showUnderSelection = selectionEnd == editor.caretModel.offset
        val preferredOffset = if (!showUnderSelection) {
            editor.selectionModel.selectionStart
        } else {
            editor.selectionModel.selectionEnd
        }

        val point = editor.offsetToXY(preferredOffset)
        val visibleArea = editor.scrollingModel.visibleArea

        return visibleArea.contains(point) == showUnderSelection
    }

    /**
     * Get line by visual start position
     */
    private fun getLineByVisualStart(editor: Editor, offset: Int, skipLineStartOffset: Boolean): Int {
        val visualPosition = editor.offsetToVisualPosition(offset)
        val skipCurrentLine = skipLineStartOffset && visualPosition.column == 0

        val line = if (skipCurrentLine) {
            max(visualPosition.line - 1, 0)
        } else {
            visualPosition.line
        }

        val lineStartPosition = VisualPosition(line, 0)
        return editor.visualToLogicalPosition(lineStartPosition).line
    }

    /**
     * Get offset for a specific line
     */
    private fun getOffsetForLine(editor: Editor, line: Int): Int {
        val document = editor.document
        val lineStart = document.getLineStartOffset(line)
        val lineEnd = document.getLineEndOffset(line)
        val lineText = document.getText(TextRange(lineStart, lineEnd))

        var textIndex = -1

        // Find first non-whitespace character
        for (i in lineText.indices) {
            if (!Character.isWhitespace(lineText[i])) {
                textIndex = i
                break
            }
        }

        val textIndex2 = min(HORIZONTAL_GAP, textIndex)
        return if (textIndex2 < 0) lineStart else lineStart + textIndex2
    }

    /**
     * Calculate the X position for the hint
     */
    private fun calculateXPosition(desiredX: Int, hintWidth: Int, contentWidth: Int): Int {
        return min(max(MARGIN, desiredX), contentWidth - hintWidth - MARGIN)
    }

    fun showEditorHint(
        hint: LightweightHint,
        editor: Editor,
        @PositionFlags constraint: Short,
        @HintManager.HideFlags flags: Int,
        timeout: Int,
        reviveOnEditorChange: Boolean,
        point: Point
    ) {
        // val pos: LogicalPosition = editor.caretModel.logicalPosition
        // val p = HintManagerImpl.getHintPosition(hint, editor, pos, constraint)
        HintManagerImpl.getInstanceImpl().showEditorHint(
            hint,
            editor,
            point,
            flags,
            timeout,
            reviveOnEditorChange,
            HintManagerImpl.createHintHint(editor, point, hint, constraint)
        )
    }

    fun calculateTooltipPosition(editor: Editor): Point {
        val model = editor.selectionModel
        val document = editor.document
        val caretOffset = editor.caretModel.offset

        val selectionStart = model.selectionStart
        val selectionEnd = model.selectionEnd

        val endLine = document.getLineNumber(selectionEnd)

        // Get cursor visual position
        val caretVisualPosition = editor.offsetToVisualPosition(caretOffset)
        val caretXY = editor.visualPositionToXY(caretVisualPosition)

        // Determine if cursor is at the start or end of selection
        val isCursorAtStart = caretOffset == selectionStart
        val isCursorAtEnd = caretOffset == selectionEnd

        // Calculate X position: cursor position + 16px offset
        val tooltipX = if (isCursorAtStart) {
            caretXY.x + 16
        } else {
            val lineStartOffset = getLineFirstNonWhitespaceOffset(document, endLine)
            val position = editor.offsetToVisualPosition(lineStartOffset)
            maxOf(editor.visualPositionToXY(position).x + 250, caretXY.x + 16)
        }

        val tooltipY = when {
            isCursorAtStart -> {
                // Cursor at start: show tooltip above selection
                val selectionStartLine = document.getLineNumber(selectionStart)
                val selectionTopY = editor.logicalPositionToXY(LogicalPosition(selectionStartLine, 0)).y
                selectionTopY - editor.lineHeight // Above the selection
            }

            else -> {
                // Cursor at end: show tooltip below selection
                val selectionEndLine = document.getLineNumber(selectionEnd)
                val selectionBottomY = editor.logicalPositionToXY(LogicalPosition(selectionEndLine, 0)).y
                selectionBottomY + editor.lineHeight * 2 // Below the selection
            }
        }

        return Point(tooltipX, tooltipY)
    }

    private fun getLineFirstNonWhitespaceOffset(document: Document, lineNumber: Int): Int {
        val lineStartOffset = document.getLineStartOffset(lineNumber)
        val lineEndOffset = document.getLineEndOffset(lineNumber)
        val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))

        // 找到第一个非空白字符的索引
        val firstNonWhitespaceIndex = lineText.indexOfFirst { !it.isWhitespace() }

        return if (firstNonWhitespaceIndex >= 0) {
            lineStartOffset + firstNonWhitespaceIndex
        } else {
            // 如果整行都是空白字符，返回
            lineStartOffset
        }
    }

}
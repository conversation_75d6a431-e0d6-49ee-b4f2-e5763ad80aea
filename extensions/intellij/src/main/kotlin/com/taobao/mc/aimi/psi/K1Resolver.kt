@file:Suppress("DEPRECATION")

package com.taobao.mc.aimi.psi

import com.intellij.openapi.components.serviceOrNull
import com.intellij.psi.PsiClass
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.util.PsiTreeUtil
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.psi.types.*
import org.jetbrains.kotlin.builtins.getValueParameterTypesFromFunctionType
import org.jetbrains.kotlin.builtins.isFunctionType
import org.jetbrains.kotlin.codegen.isJvmStaticInObjectOrClassOrInterface
import org.jetbrains.kotlin.descriptors.*
import org.jetbrains.kotlin.descriptors.impl.LocalVariableDescriptor
import org.jetbrains.kotlin.idea.caches.resolve.analyze
import org.jetbrains.kotlin.idea.util.IdeDescriptorRenderers
import org.jetbrains.kotlin.lexer.KtTokens
import org.jetbrains.kotlin.psi.*
import org.jetbrains.kotlin.psi.psiUtil.getParentOfType
import org.jetbrains.kotlin.renderer.DescriptorRenderer
import org.jetbrains.kotlin.resolve.BindingContext
import org.jetbrains.kotlin.resolve.DescriptorToSourceUtils
import org.jetbrains.kotlin.resolve.descriptorUtil.fqNameOrNull
import org.jetbrains.kotlin.resolve.lazy.BodyResolveMode
import org.jetbrains.kotlin.types.KotlinType
import org.jetbrains.kotlin.types.TypeUtils
import org.jetbrains.kotlin.util.classNameAndMessage

/**
 * Kotlin 对象类型解析器
 * 专门处理 Kotlin 语言的类型解析和 Lambda 表达式
 */
class K1Resolver : KotlinResolver {
    private val javaResolver by lazy { serviceOrNull<JavaObjectTypeResolver>() }

    override val logger = LoggerManager.getLogger(javaClass)
    override val resolverModel: String
        get() = "Kotlin (K1) Resolver"

    /**
     * 解析 Kotlin Lambda 参数
     */
    override fun resolveLambdaParameters(lambdaExpression: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()
        if (lambdaExpression !is KtLambdaExpression) return objects

        try {
            val bindingContext = lambdaExpression.analyze()

            // 获取 Lambda 参数
            val valueParameters = lambdaExpression.valueParameters
            valueParameters.forEach { param ->
                val type = bindingContext.get(BindingContext.VALUE_PARAMETER, param)?.type
                objects.add(
                    ObjectInfo(
                        name = param.name ?: "it",
                        type = type?.toString() ?: param.typeReference?.text ?: "Any",
                        qualifiedType = type?.constructor?.declarationDescriptor?.fqNameOrNull()?.asString(),
                        kind = ObjectKind.LAMBDA_PARAMETER,
                        element = param,
                        members = if (type != null) extractKotlinTypeMembers(type, bindingContext) else emptyList(),
                        isNullable = type?.isMarkedNullable ?: false,
                        confidence = 1.0,
                        isLambdaParameter = true,
                        lambdaContext = "Kotlin Lambda",
                        filepath = getTypeFilePath(type) ?: param.containingFile.virtualFile?.toUriOrNull(),
                        content = getSpecialTypeSourceCode(type)
                    )
                )
            }

            // 如果没有显式参数，检查是否有隐式 'it' 参数
            if (objects.isEmpty()) {
                // 尝试推断 'it' 参数的类型
                val itType = inferItParameterType(lambdaExpression, bindingContext)
                if (itType != null) {
                    objects.add(
                        ObjectInfo(
                            name = "it",
                            type = renderType(itType),
                            qualifiedType = itType.constructor.declarationDescriptor?.fqNameOrNull()?.asString(),
                            kind = ObjectKind.LAMBDA_PARAMETER,
                            element = lambdaExpression,
                            members = extractKotlinTypeMembers(itType, bindingContext),
                            isNullable = itType.isMarkedNullable,
                            confidence = 0.9,
                            isLambdaParameter = true,
                            lambdaContext = "Kotlin Lambda (it parameter)",
                            filepath = getTypeFilePath(itType),
                            content = getSpecialTypeSourceCode(itType)
                        )
                    )
                }
            }

            // 检查是否有接收者类型（扩展函数样式的 Lambda）
            val receiverType = inferLambdaReceiverType(lambdaExpression, bindingContext)
            if (receiverType != null) {
                objects.add(
                    ObjectInfo(
                        name = "this",
                        type = renderType(receiverType),
                        qualifiedType = receiverType.constructor.declarationDescriptor?.fqNameOrNull()?.asString(),
                        kind = ObjectKind.LAMBDA_RECEIVER,
                        element = lambdaExpression,
                        members = extractKotlinTypeMembers(receiverType, bindingContext),
                        isNullable = receiverType.isMarkedNullable,
                        confidence = 1.0,
                        isLambdaParameter = false,
                        lambdaContext = "Kotlin Lambda Receiver",
                        filepath = getTypeFilePath(receiverType),
                        content = getSpecialTypeSourceCode(receiverType)
                    )
                )
            }

        } catch (e: Exception) {
            logger.debug("Failed to resolve Kotlin lambda parameters, ${e.classNameAndMessage}")
        }

        return objects
    }

    /**
     * 解析Kotlin表达式类型
     */
    override fun resolveExpressionType(expression: KtExpression): ObjectInfo? {
        try {
            val bindingContext = expression.analyze()
            val type = bindingContext.getType(expression)
                ?: bindingContext.get(BindingContext.EXPRESSION_TYPE_INFO, expression)?.type
                ?: bindingContext.get(BindingContext.SMARTCAST, expression)?.defaultType

            return when (expression) {
                is KtThisExpression -> {
                    val containingClass = PsiTreeUtil.getParentOfType(expression, KtClass::class.java)
                    ObjectInfo(
                        name = "this",
                        type = containingClass?.name ?: "Unknown",
                        qualifiedType = containingClass?.fqName?.asString(),
                        kind = ObjectKind.THIS,
                        element = containingClass,
                        members = extractKotlinClassMembers(containingClass),
                        confidence = 1.0,
                        filepath = containingClass?.containingFile?.virtualFile?.toUriOrNull(),
                        content = getSpecialTypeSourceCode(type)
                    )
                }

                is KtSuperExpression -> {
                    val containingClass = PsiTreeUtil.getParentOfType(expression, KtClass::class.java)
                    val superTypes = containingClass?.superTypeListEntries
                    val superType = superTypes?.firstOrNull()

                    // 解析父类型的实际类型
                    val resolvedSuperType = superType?.let { entry ->
                        val typeReference = entry.typeReference
                        typeReference?.let { ref ->
                            bindingContext.get(BindingContext.TYPE, ref)
                        }
                    }

                    val superTypeName = resolvedSuperType?.let { renderType(it) } ?: superType?.text ?: "Any"

                    val qualifiedTypeName = resolvedSuperType?.constructor?.declarationDescriptor?.fqNameOrNull()?.asString()

                    // 提取父类成员
                    val superMembers = resolvedSuperType?.let { type ->
                        extractKotlinTypeMembers(type, bindingContext)
                    } ?: emptyList()

                    // 获取文件路径
                    val filepath = resolvedSuperType?.let { getTypeFilePath(it) }

                    ObjectInfo(
                        name = "super",
                        type = superTypeName,
                        qualifiedType = qualifiedTypeName,
                        kind = ObjectKind.SUPER,
                        element = superType,
                        members = superMembers,
                        confidence = 1.0,
                        filepath = filepath
                    )
                }

                is KtNameReferenceExpression -> {
                    val resolved = bindingContext.get(BindingContext.REFERENCE_TARGET, expression)
                    if (type != null) {
                        // 获取更友好的类型名称
                        val typeName = renderType(type)
                        val variableName = expression.getReferencedName()

                        logger.debug("Resolved Kotlin reference: $variableName -> $typeName")

                        ObjectInfo(
                            name = variableName,
                            type = typeName,
                            qualifiedType = type.constructor.declarationDescriptor?.fqNameOrNull()?.asString(),
                            kind = determineKotlinObjectKind(resolved),
                            element = expression,
                            members = extractKotlinTypeMembers(type, bindingContext),
                            isNullable = type.isMarkedNullable,
                            confidence = 0.9,
                            filepath = getTypeFilePath(type),
                            content = getSpecialTypeSourceCode(type)
                        )
                    } else {
                        // 获取类型描述符
                        val classDescriptor = resolved as? ClassDescriptor
                        // 转换为 PSI 元素
                        val psiElement = classDescriptor?.let { descriptor ->
                            DescriptorToSourceUtils.descriptorToDeclaration(descriptor)
                        }

                        // 检查具体类型
                        return when (psiElement) {
                            is KtClass -> {
                                // Kotlin 类
                                val kotlinClass = psiElement
                                val qualifiedName = kotlinClass.fqName?.asString()

                                ObjectInfo(
                                    name = expression.getReferencedName(),
                                    type = kotlinClass.name ?: "Unknown",
                                    qualifiedType = qualifiedName,
                                    kind = determineKotlinObjectKind(resolved),
                                    element = kotlinClass,
                                    members = extractKotlinClassMembers(kotlinClass),
                                    confidence = 0.9,
                                    filepath = kotlinClass.containingFile.virtualFile?.toUriOrNull(),
                                    content = getSpecialTypeSourceCode(type)
                                )
                            }

                            is PsiClass -> {
                                // Java 类 - 委托给 Java 解析器
                                val javaClass = psiElement
                                val qualifiedName = javaClass.qualifiedName

                                ObjectInfo(
                                    name = expression.getReferencedName(),
                                    type = javaClass.name ?: "Unknown",
                                    qualifiedType = qualifiedName,
                                    kind = determineKotlinObjectKind(resolved),
                                    element = javaClass,
                                    members = javaResolver?.extractJavaClassMembers(javaClass) ?: emptyList(),
                                    confidence = 0.9,
                                    filepath = javaClass.containingFile.virtualFile?.toUriOrNull()
                                )
                            }

                            else -> null
                        }
                    }
                }

                else -> {
                    if (type != null) {
                        ObjectInfo(
                            name = "expression",
                            type = type.toString(),
                            qualifiedType = type.constructor.declarationDescriptor?.fqNameOrNull()?.asString(),
                            kind = ObjectKind.EXPRESSION,
                            element = expression,
                            members = extractKotlinTypeMembers(type, bindingContext),
                            isNullable = type.isMarkedNullable,
                            confidence = 0.7,
                            filepath = getTypeFilePath(type),
                            content = getSpecialTypeSourceCode(type)
                        )
                    } else null
                }
            }
        } catch (e: Exception) {
            logger.debug("Failed to resolve Kotlin expression type, ${e.classNameAndMessage}")
            return null
        }
    }

    /**
     * 查找可用的Kotlin对象
     */
    override fun findAvailableKotlinObjects(element: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        // 添加this对象
        val containingClass = PsiTreeUtil.getParentOfType(element, KtClass::class.java)
        val containingFunction = PsiTreeUtil.getParentOfType(element, KtNamedFunction::class.java)

        if (containingClass != null) {
            objects.add(
                ObjectInfo(
                    name = "this",
                    type = containingClass.name ?: "Unknown",
                    qualifiedType = containingClass.fqName?.asString(),
                    kind = ObjectKind.THIS,
                    element = containingClass,
                    members = extractKotlinClassMembers(containingClass),
                    confidence = 1.0,
                    filepath = containingClass.containingFile.virtualFile?.toUriOrNull(),
                    content = containingClass.takeIf { it.isData() || it.isSealed() || it.isEnum() }?.text
                )
            )
        }

        // 添加函数参数
        containingFunction?.valueParameters?.forEach { param ->
            try {
                val bindingContext = param.analyze()
                val type = bindingContext.get(BindingContext.VALUE_PARAMETER, param)?.type

                objects.add(
                    ObjectInfo(
                        name = param.name ?: "",
                        type = type?.toString() ?: param.typeReference?.text ?: "Any",
                        qualifiedType = type?.constructor?.declarationDescriptor?.fqNameOrNull()?.asString(),
                        kind = ObjectKind.PARAMETER,
                        element = param,
                        members = if (type != null) extractKotlinTypeMembers(type, bindingContext) else emptyList(),
                        isNullable = type?.isMarkedNullable ?: false,
                        confidence = 0.9,
                        filepath = getTypeFilePath(type),
                        content = getSpecialTypeSourceCode(type)
                    )
                )
            } catch (e: Exception) {
                logger.debug("Failed to analyze Kotlin parameter", e)
            }
        }

        // 添加局部变量（简化实现）
        var current: PsiElement? = element
        while (current != null) {
            if (current is KtBlockExpression) {
                current.statements.forEach { statement ->
                    if (statement is KtProperty) {
                        try {
                            val bindingContext = statement.analyze()
                            val type = bindingContext.get(BindingContext.VARIABLE, statement)?.type

                            objects.add(
                                ObjectInfo(
                                    name = statement.name ?: "",
                                    type = renderType(type, statement.typeReference?.text ?: "Any"),
                                    qualifiedType = type?.constructor?.declarationDescriptor?.fqNameOrNull()?.asString(),
                                    kind = ObjectKind.LOCAL_VARIABLE,
                                    element = statement,
                                    members = if (type != null) extractKotlinTypeMembers(type, bindingContext) else emptyList(),
                                    isNullable = type?.isMarkedNullable ?: false,
                                    confidence = 0.8,
                                    filepath = getTypeFilePath(type),
                                    content = getSpecialTypeSourceCode(type)
                                )
                            )
                        } catch (e: Exception) {
                            logger.debug("Failed to analyze Kotlin property", e)
                        }
                    }
                }
            }
            current = current.parent
        }

        return objects
    }

    /**
     * 推断 'it' 参数类型
     */
    private fun inferItParameterType(lambdaExpression: KtLambdaExpression, bindingContext: BindingContext): KotlinType? {
        try {
            // 1. 从 BindingContext 获取 lambda 的类型信息
            val lambdaType = bindingContext.getType(lambdaExpression)
            if (lambdaType != null && lambdaType.isFunctionType) {
                val valueParameters = lambdaType.getValueParameterTypesFromFunctionType()
                if (valueParameters.isNotEmpty()) {
                    return valueParameters.first().type
                }
            }

            // 2. 尝试从父级调用表达式推断
            val parent = lambdaExpression.parent
            if (parent is KtValueArgument) {
                // val callExpression = parent.parent?.parent as? KtCallExpression
                val callExpression = findLambdaCallContext(lambdaExpression)
                // 尝试从解析的函数描述符获取参数类型
                val resolvedCall = bindingContext.get(BindingContext.CALL, callExpression)?.let { call ->
                    bindingContext.get(BindingContext.RESOLVED_CALL, call)
                }
                resolvedCall?.let { call ->
                    val functionDescriptor = call.resultingDescriptor as? FunctionDescriptor
                    functionDescriptor?.let { descriptor ->
                        // 获取Lambda参数对应的参数描述符
                        val lambdaParam = descriptor.valueParameters.lastOrNull()
                        if (lambdaParam != null) {
                            val paramType = lambdaParam.type
                            // 如果是函数类型，提取其第一个参数类型
                            val arguments = paramType.arguments
                            if (arguments.isNotEmpty()) {
                                return arguments.first().type
                            }
                        }
                    }
                }
            }

            // 3. 尝试从扩展函数的接收者类型推断
            if (parent is KtDotQualifiedExpression) {
                val receiverExpression = parent.receiverExpression
                val receiverType = bindingContext.getType(receiverExpression) ?: run {
                    bindingContext.get(BindingContext.EXPECTED_EXPRESSION_TYPE, receiverExpression)
                }
                if (receiverType != null) {
                    return receiverType
                }
            }

            // 4. 尝试从赋值语句推断
            val assignment = lambdaExpression.getParentOfType<KtBinaryExpression>(false)
            val expression = assignment?.left
            if (assignment?.operationToken == KtTokens.EQ && expression != null) {
                val leftType = bindingContext.getType(expression) ?: run {
                    bindingContext.get(BindingContext.EXPECTED_EXPRESSION_TYPE, expression)
                }
                if (leftType != null && leftType.isFunctionType) {
                    val parameterTypes = leftType.getValueParameterTypesFromFunctionType()
                    if (parameterTypes.isNotEmpty()) {
                        return parameterTypes.first().type
                    }
                }
            }

        } catch (e: Exception) {
            logger.debug("Failed to infer 'it' parameter type", e)
        }

        return null
    }

    /**
     * 推断 Lambda 接收者类型
     */
    private fun inferLambdaReceiverType(lambdaExpression: KtLambdaExpression, bindingContext: BindingContext): KotlinType? {
        // 尝试推断 Lambda 的接收者类型（用于扩展函数样式的 Lambda）
        try {
            // 从调用上下文推断接收者类型
            val callContext = findLambdaCallContext(lambdaExpression)
            if (callContext != null) {
                val receiverType = inferReceiverTypeFromCallContext(callContext, bindingContext)
                if (receiverType != null) {
                    logger.debug("Inferred lambda receiver type: ${renderType(receiverType)}")
                    return receiverType
                }
            }

            // 尝试从Lambda表达式的函数类型中推断接收者类型
            val lambdaType = bindingContext.getType(lambdaExpression)
            if (lambdaType != null) {
                val receiverType = extractReceiverTypeFromFunctionType(lambdaType)
                if (receiverType != null) {
                    logger.debug("Extracted receiver type from function type: ${renderType(receiverType)}")
                    return receiverType
                }
            }

        } catch (e: Exception) {
            logger.debug("Failed to infer lambda receiver type", e)
        }
        return null
    }

    /**
     * 提取Kotlin类的成员
     */
    private fun extractKotlinClassMembers(ktClass: KtClass?): List<MemberInfo> {
        if (ktClass == null) return emptyList()
        val bindingContext = ktClass.analyze()

        val members = mutableListOf<MemberInfo>()

        // 添加属性
        ktClass.getProperties().forEach { property ->
            val propertyType = bindingContext.get(BindingContext.VARIABLE, property)?.type
            members.add(
                MemberInfo(
                    name = property.name ?: "",
                    type = renderType(propertyType),
                    kind = MemberKind.PROPERTY,
                    visibility = getKotlinVisibility(property),
                    documentation = property.docComment?.text
                )
            )
        }

        // 添加函数
        ktClass.declarations.filterIsInstance<KtNamedFunction>().forEach { function ->
            val functionType = bindingContext.get(BindingContext.FUNCTION, function)?.returnType
            members.add(
                MemberInfo(
                    name = function.name ?: "",
                    type = renderType(functionType, "Unit"),
                    kind = MemberKind.METHOD,
                    visibility = getKotlinVisibility(function),
                    signature = buildKotlinMethodSignature(function),
                    documentation = function.docComment?.text
                )
            )
        }

        // 添加伴生对象中的静态函数
        ktClass.getCompanionObjects().forEach { companionObject ->
            companionObject.declarations.filterIsInstance<KtNamedFunction>().forEach { function ->
                val functionType = bindingContext.get(BindingContext.FUNCTION, function)?.returnType
                members.add(
                    MemberInfo(
                        name = "Companion." + (function.name ?: "unknown"),
                        type = renderType(functionType, "Unit"),
                        kind = MemberKind.METHOD,
                        visibility = getKotlinVisibility(function),
                        signature = "Companion." + buildKotlinMethodSignature(function),
                        documentation = function.docComment?.text,
                        isStatic = true
                    )
                )
            }
            companionObject.declarations.filterIsInstance<KtProperty>().forEach { property ->
                val propertyType = bindingContext.get(BindingContext.VARIABLE, property)?.type
                members.add(
                    MemberInfo(
                        name = "Companion." + (property.name ?: "unknown"),
                        type = renderType(propertyType),
                        kind = MemberKind.PROPERTY,
                        visibility = getKotlinVisibility(property),
                        documentation = property.docComment?.text
                    )
                )
            }
        }

        return members
    }

    /**
     * 提取Kotlin类型的成员
     */
    private fun extractKotlinTypeMembers(type: KotlinType, bindingContext: BindingContext): List<MemberInfo> {
        val members = mutableListOf<MemberInfo>()

        try {
            val classDescriptor = TypeUtils.getClassDescriptor(type)
            classDescriptor?.let { descriptor ->
                // 获取所有成员
                val memberScope = descriptor.unsubstitutedMemberScope
                val allMembers = memberScope.getContributedDescriptors()

                allMembers.forEach { memberDescriptor ->
                    when (memberDescriptor) {
                        is PropertyDescriptor -> {
                            members.add(
                                MemberInfo(
                                    name = memberDescriptor.name.asString(),
                                    type = renderType(memberDescriptor.type),
                                    kind = MemberKind.PROPERTY,
                                    isStatic = memberDescriptor.isJvmStaticInObjectOrClassOrInterface(),
                                    visibility = memberDescriptor.visibility.name
                                )
                            )
                        }

                        is FunctionDescriptor -> {
                            members.add(
                                MemberInfo(
                                    name = memberDescriptor.name.asString(),
                                    type = renderType(memberDescriptor.returnType, "Unit"),
                                    kind = MemberKind.METHOD,
                                    visibility = memberDescriptor.visibility.name,
                                    isStatic = memberDescriptor.isJvmStaticInObjectOrClassOrInterface(),
                                    signature = DescriptorRenderer.COMPACT_WITH_SHORT_TYPES.render(memberDescriptor).substringBefore("defined").trimEnd(),
                                )
                            )
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.debug("Failed to extract Kotlin type members", e)
        }

        return members
    }

    /**
     * 确定Kotlin对象种类
     */
    private fun determineKotlinObjectKind(resolved: DeclarationDescriptor?): ObjectKind {
        return when (resolved) {
            is LocalVariableDescriptor -> ObjectKind.LOCAL_VARIABLE
            is ValueParameterDescriptor -> ObjectKind.PARAMETER
            is PropertyDescriptor -> ObjectKind.FIELD
            else -> ObjectKind.LOCAL_VARIABLE
        }
    }

    /**
     * 获取Kotlin可见性
     */
    private fun getKotlinVisibility(member: KtModifierListOwner): String {
        return when {
            member.hasModifier(KtTokens.PUBLIC_KEYWORD) -> "public"
            member.hasModifier(KtTokens.PRIVATE_KEYWORD) -> "private"
            member.hasModifier(KtTokens.PROTECTED_KEYWORD) -> "protected"
            member.hasModifier(KtTokens.INTERNAL_KEYWORD) -> "internal"
            else -> "public"
        }
    }

    /**
     * 构建Kotlin方法签名
     */
    private fun buildKotlinMethodSignature(function: KtNamedFunction): String {
        return runCatching {
            val bindingContext = function.analyze(BodyResolveMode.PARTIAL)
            val functionDescriptor = bindingContext[BindingContext.FUNCTION, function]
            functionDescriptor?.let {
                DescriptorRenderer.COMPACT_WITH_SHORT_TYPES.render(functionDescriptor).substringBefore("defined").trimEnd()
            }
        }.getOrNull() ?: run {
            // 回退到原始实现
            val params = function.valueParameters.joinToString(", ") { param ->
                "${param.name}: ${param.typeReference?.text ?: "Any"}"
            }
            "${function.name}($params): ${function.typeReference?.text ?: "Unit"}"
        }
    }

    private fun getTypeFilePath(type: KotlinType?): String? {
        val descriptor = type?.constructor?.declarationDescriptor
        return when (descriptor) {
            is ClassDescriptor -> {
                val psiClass = DescriptorToSourceUtils.descriptorToDeclaration(descriptor)
                psiClass?.containingFile?.virtualFile?.toUriOrNull()
            }

            else -> null
        }
    }


    private fun renderType(kotlinType: KotlinType?, ifNull: String = "Any"): String {
        return kotlinType?.let {
            IdeDescriptorRenderers.SOURCE_CODE_TYPES_WITH_SHORT_NAMES.renderType(it)
            // IdeDescriptorRenderers.SOURCE_CODE.renderType(it)
            // it.constructor.declarationDescriptor?.name?.asString()
            // kotlinType.toString()
        } ?: ifNull
    }

    /**
     * 查找Lambda表达式的调用上下文
     */
    private fun findLambdaCallContext(lambdaExpression: KtLambdaExpression): KtCallExpression? {
        var parent = lambdaExpression.parent

        // 向上查找直到找到函数调用表达式
        while (parent != null) {
            when (parent) {
                is KtCallExpression -> return parent
                is KtDotQualifiedExpression -> {
                    val selectorExpression = parent.selectorExpression
                    if (selectorExpression is KtCallExpression) {
                        return selectorExpression
                    }
                }
            }
            parent = parent.parent
        }
        return null
    }


    /**
     * 从调用上下文推断接收者类型
     */
    private fun inferReceiverTypeFromCallContext(callExpression: KtCallExpression, bindingContext: BindingContext): KotlinType? {
        try {
            val calleeExpression = callExpression.calleeExpression
            if (calleeExpression is KtNameReferenceExpression) {
                val functionName = calleeExpression.getReferencedName()

                // 处理标准库的作用域函数
                when (functionName) {
                    "with" -> {
                        // with函数的Lambda接收者类型是第一个参数类型
                        val firstArg = callExpression.valueArguments.firstOrNull()?.getArgumentExpression()
                        if (firstArg != null) {
                            return bindingContext.getType(firstArg)
                        }
                    }

                    else -> {
                        // apply和run函数的Lambda接收者类型是调用者类型
                        getReceiverTypeFromQualifiedExpression(callExpression, bindingContext)?.let {
                            return it
                        }
                    }
                }
            }

            // 尝试从解析的函数描述符获取接收者类型
            val resolvedCall = bindingContext.get(BindingContext.CALL, callExpression)?.let { call ->
                bindingContext.get(BindingContext.RESOLVED_CALL, call)
            }
            resolvedCall?.let { call ->
                val functionDescriptor = call.resultingDescriptor as? FunctionDescriptor
                functionDescriptor?.let { descriptor ->
                    // 获取Lambda参数对应的参数描述符
                    val lambdaParam = descriptor.valueParameters.lastOrNull()
                    if (lambdaParam != null) {
                        val paramType = lambdaParam.type
                        // 检查是否是带接收者的函数类型
                        return extractReceiverTypeFromFunctionType(paramType)
                    }
                }
            }

        } catch (e: Exception) {
            logger.debug("Failed to infer receiver type from call context", e)
        }
        return null
    }

    /**
     * 从限定表达式获取接收者类型
     */
    private fun getReceiverTypeFromQualifiedExpression(callExpression: KtCallExpression, bindingContext: BindingContext): KotlinType? {
        val parent = callExpression.parent
        if (parent is KtDotQualifiedExpression) {
            val receiverExpression = parent.receiverExpression
            return bindingContext.getType(receiverExpression)
        }
        return null
    }

    /**
     * 获取集合元素类型
     */
    private fun getCollectionElementType(callExpression: KtCallExpression, bindingContext: BindingContext): KotlinType? {
        val receiverType = getReceiverTypeFromQualifiedExpression(callExpression, bindingContext)
        if (receiverType != null) {
            // 尝试提取集合的元素类型
            val arguments = receiverType.arguments
            if (arguments.isNotEmpty()) {
                return arguments.first().type
            }
        }
        return null
    }

    /**
     * 从函数类型中提取接收者类型
     */
    private fun extractReceiverTypeFromFunctionType(functionType: KotlinType): KotlinType? {
        try {
            // 检查是否是扩展函数类型（带接收者的函数类型）
            val classDescriptor = functionType.constructor.declarationDescriptor
            if (classDescriptor != null) {
                val fqName = classDescriptor.fqNameOrNull()?.asString()
                // 扩展函数类型通常以kotlin.Function开头，并且有接收者参数
                if (fqName?.startsWith("kotlin.Function") == true) {
                    val arguments = functionType.arguments
                    if (arguments.size >= 2) {
                        // 对于扩展函数类型 T.() -> R，第一个类型参数是接收者类型T
                        return arguments.firstOrNull()?.type
                    }
                }
            }
        } catch (e: Exception) {
            logger.debug("Failed to extract receiver type from function type", e)
        }
        return null
    }

    /**
     * 检查是否是扩展函数类型
     */
    private fun isExtensionFunctionType(type: KotlinType): Boolean {
        try {
            // 在Kotlin类型系统中，扩展函数类型有特殊的标记
            // 可以通过检查类型的注解或其他属性来判断
            return type.annotations.any { annotation ->
                annotation.fqName?.asString() == "kotlin.ExtensionFunctionType"
            }
        } catch (e: Exception) {
            logger.debug("Failed to check if extension function type", e)
            return false
        }
    }

    private fun getSpecialTypeSourceCode(type: KotlinType?): String? {
        return try {
            type?.constructor?.declarationDescriptor?.let { descriptor ->
                val psiElement = DescriptorToSourceUtils.descriptorToDeclaration(descriptor)
                (psiElement as? KtClass)?.takeIf {
                    // 检查是否为密封类、数据类或枚举类
                    it.isData() || it.isEnum() || it.isSealed()
                }?.text
            }
        } catch (e: Exception) {
            logger.debug("Failed to get special type source code", e)
            null
        }
    }
}


package com.taobao.mc.aimi.listeners

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.application
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.util.mSelectedTextEditor
import kotlinx.coroutines.launch
import kotlin.properties.Delegates

/**
 * 监听活动编辑器变化的监听器
 * 当文件打开、关闭或选择变化时，检测当前激活的编辑器是否发生变化
 * 如果有变化，向 webview 发送通知
 * 如果 webview 还未就绪，会将编辑器引用存储起来，等 webview 就绪后再发送
 */
class ActiveEditorChangeListener(private val project: Project) : FileEditorManagerListener, DumbAware {

    private val logger = LoggerManager.getLogger(javaClass)
    private var lastActiveFile: VirtualFile? = null

    private val continuePluginService = project.service<AIMIPluginService>()

    private var isWebviewReady by Delegates.observable(false) { _, _, newValue ->
        if (!newValue) return@observable
        checkAndNotifyEditorChange(FileEditorManager.getInstance(project))
    }

    init {
        continuePluginService.coroutineScope.launch {
            continuePluginService.allResidentWindowsWebviewReady()
            isWebviewReady = true
        }
    }

    override fun fileOpened(source: FileEditorManager, file: VirtualFile) {
        checkAndNotifyEditorChange(source)
    }

    override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
        checkAndNotifyEditorChange(source)
    }

    override fun selectionChanged(event: FileEditorManagerEvent) {
        checkAndNotifyEditorChange(event.manager)
    }

    private fun checkAndNotifyEditorChange(fileEditorManager: FileEditorManager) {
        ApplicationManager.getApplication().invokeLater {
            val currentEditor = fileEditorManager.mSelectedTextEditor
            val currentActiveFile = currentEditor?.virtualFile

            // 检查激活的编辑器是否发生变化
            if (currentActiveFile != lastActiveFile) {
                lastActiveFile = currentActiveFile

                // 发送通知
                application.messageBus.syncPublisher(IActiveEditorChangeListener.TOPIC).notifyEditorChanged(currentEditor)

                // 向 webview 发送通知
                notifyWebviewOfEditorChange(currentEditor)
            }
        }
    }

    private fun notifyWebviewOfEditorChange(editor: Editor?) {
        try {
            if (isWebviewReady) {
                // webview 已就绪，直接发送
                sendNotificationToWebview(editor)
            }
        } catch (e: Exception) {
            logger.warn("Failed to handle active editor change", e)
        }
    }

    private fun sendNotificationToWebview(editor: Editor?) {
        try {
            val activeFile = editor?.virtualFile
            val data = mapOf(
                "filepath" to (activeFile?.url ?: ""),
                "filename" to (activeFile?.name ?: ""),
                "timestamp" to System.currentTimeMillis(),
                "contents" to (editor?.document?.text ?: ""),
            )

            val continuePluginService = project.service<AIMIPluginService>()
            continuePluginService.sendToWebview(
                MessageTypes.ToWebview.ActiveEditorChanged,
                data
            )
            logger.info("Notified webview of active editor change: ${data["filename"]}")
        } catch (e: Exception) {
            logger.warn("Failed to send notification to webview", e)
        }
    }
}
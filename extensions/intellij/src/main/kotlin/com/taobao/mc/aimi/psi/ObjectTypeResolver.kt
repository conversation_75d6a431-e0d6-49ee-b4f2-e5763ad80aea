package com.taobao.mc.aimi.psi

import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.taobao.mc.aimi.logger.thisLogger
import com.taobao.mc.aimi.psi.types.MemberInfo
import com.taobao.mc.aimi.psi.types.ObjectInfo
import com.taobao.mc.aimi.psi.types.ObjectKind

/**
 * 对象类型解析器
 * 专门用于识别光标位置最合适的对象及其类型信息
 * 支持多语言和 Lambda 表达式兼容性
 */
class ObjectTypeResolver(private val project: Project) {
    companion object {
        private const val PSI_LAMBDA_EXPRESSION = "com.intellij.psi.PsiLambdaExpression"
        private const val KT_LAMBDA_EXPRESSION = "org.jetbrains.kotlin.psi.KtLambdaExpression"
        private const val JS_ARROW_FUNCTION = "com.intellij.lang.javascript.psi.JSArrowFunction"
        private const val JS_FUNCTION_EXPRESSION = "com.intellij.lang.javascript.psi.JSFunctionExpression"
        private val lambdaClassNames = setOf(PSI_LAMBDA_EXPRESSION, KT_LAMBDA_EXPRESSION, JS_ARROW_FUNCTION, JS_FUNCTION_EXPRESSION)
    }

    private val logger = thisLogger()

    // 语言特定的解析器
    val javaResolver by lazy { serviceOrNull<JavaObjectTypeResolver>() }
    val kotlinResolver by lazy {
        val resolver = if (useK2Model()) serviceOrNull<K2Resolver>()
        else serviceOrNull<K1Resolver>()
        logger.debug("Using ${resolver?.resolverModel} model")
        resolver
    }
    val javascriptResolver by lazy { serviceOrNull<JavaScriptObjectTypeResolver>() }

    /**
     * 解析光标位置的对象类型
     */
    fun resolveObjectAtCursor(editor: Editor): List<ObjectInfo> {
        try {
            val offset = editor.caretModel.offset
            val document = editor.document
            val psiFile = PsiDocumentManager.getInstance(project).getPsiFile(document) ?: return emptyList()

            val element = psiFile.findElementAt(offset) ?: return emptyList()

            val objects = mutableListOf<ObjectInfo>()
            // 优先检查 Lambda 上下文
            if (isInLambdaContext(element)) {
                val lambdaObjects = resolveLambdaContext(element)
                if (lambdaObjects.isNotEmpty()) {
                    // return lambdaObjects
                    objects.addAll(lambdaObjects)
                }
            }

            // 委托给语言特定的解析器
            val found = when {
                isJavaFile(psiFile) -> javaResolver?.resolveObjects(psiFile, element)
                isKotlinFile(psiFile) -> kotlinResolver?.resolveObjects(psiFile, element)
                isTypeScriptFile(psiFile) -> javascriptResolver?.resolveObjects(psiFile, element)
                else -> emptyList()
            } ?: emptyList()
            if (found.isNotEmpty()) {
                objects.addAll(found)
            }
            return objects.sortedByDescending { it.confidence }
        } catch (e: Exception) {
            logger.warn("resolveObjectAtCursor failed", e)
            return emptyList()
        }
    }


    /**
     * 查找最佳匹配的对象
     */
    fun findBestMatchingObject(editor: Editor): List<ObjectInfo> {
        val objects = resolveObjectAtCursor(editor)

        // 如果有方法调用接收者，优先返回它
        val methodCallReceiver = objects.filter { it.kind == ObjectKind.METHOD_CALL }
        if (methodCallReceiver.isNotEmpty()) {
            return methodCallReceiver
        }

        // 如果有明确的点号上下文，返回点号前的对象
        // 检查是否在点号后面
        val dotContext = objects.filter { it.kind == ObjectKind.DOT_CONTEXT }
        if (dotContext.isNotEmpty()) {
            return dotContext
        }

        val lambdaReceiver = objects.filter { it.kind == ObjectKind.LAMBDA_RECEIVER }
        if (lambdaReceiver.isNotEmpty()) {
            return lambdaReceiver
        }

        val lambdaParameter = objects.filter { it.kind == ObjectKind.LAMBDA_PARAMETER }
        if (lambdaParameter.isNotEmpty()) {
            return lambdaParameter
        }

        // 没有点号上下文，优先返回this对象
        val thisObject = objects.filter { it.kind == ObjectKind.THIS }
        if (thisObject.isNotEmpty()) {
            return thisObject
        }

        // 返回所有的对象信息
        return objects
    }

    /**
     * 获取对象的可访问成员
     */
    fun getAccessibleMembers(objectInfo: ObjectInfo, includePrivate: Boolean): List<MemberInfo> {
        return if (includePrivate) {
            objectInfo.members
        } else {
            objectInfo.members.filter { it.visibility != "private" && it.visibility != "protected" }
        }
    }

    /**
     * 根据名称过滤成员
     */
    fun filterMembersByName(members: List<MemberInfo>, namePrefix: String): List<MemberInfo> {
        return members.filter { it.name.contains(namePrefix, ignoreCase = true) }
    }


    /**
     * 检测是否在 Lambda 表达式内部
     */
    private fun isInLambdaContext(element: PsiElement): Boolean {
        var current: PsiElement? = element
        while (current != null) {
            if (lambdaClassNames.contains(current::class.java.name)) {
                return true
            }
            current = current.parent
        }
        return false
    }

    /**
     * 解析 Lambda 上下文中的对象
     */
    private fun resolveLambdaContext(element: PsiElement): List<ObjectInfo> {
        val lambdaObjects = mutableListOf<ObjectInfo>()

        var current: PsiElement? = element
        while (current != null) {
            val className = current::class.java.name
            when (className) {
                // Java Lambda 表达式
                PSI_LAMBDA_EXPRESSION -> {
                    lambdaObjects.addAll(javaResolver?.resolveLambdaParameters(current) ?: emptyList())
                    break
                }
                // Kotlin Lambda 表达式
                KT_LAMBDA_EXPRESSION -> {
                    lambdaObjects.addAll(kotlinResolver?.resolveLambdaParameters(current) ?: emptyList())
                    break
                }
                // JavaScript/TypeScript 函数表达式
                JS_FUNCTION_EXPRESSION -> {
                    lambdaObjects.addAll(javascriptResolver?.resolveLambdaParameters(current) ?: emptyList())
                    break
                }
            }
            current = current.parent
        }

        return lambdaObjects
    }

    // 辅助方法
    private fun isJavaFile(psiFile: PsiFile): Boolean = psiFile.language.id.equals("JAVA", ignoreCase = true)
    private fun isKotlinFile(psiFile: PsiFile): Boolean = psiFile.language.id.equals("kotlin", ignoreCase = true)
    private fun isTypeScriptFile(psiFile: PsiFile): Boolean =
        psiFile.language.id.equals("TypeScript", ignoreCase = true) ||
                psiFile.language.id.equals("JavaScript", ignoreCase = true)

    private fun useK2Model(): Boolean {
        return runCatching {
            Class.forName("org.jetbrains.kotlin.analysis.api.types.KaType")
            System.getProperty("idea.kotlin.plugin.use.k2").toBoolean()
        }.getOrElse { false }
    }
}
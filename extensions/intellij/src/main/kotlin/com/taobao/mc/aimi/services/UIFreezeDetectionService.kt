package com.taobao.mc.aimi.services

import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.logger.UIFreezeLogger
import com.taobao.mc.aimi.settings.UIFreezeDetectionSettings
import com.taobao.mc.aimi.util.ThreadDumpAnalyzer
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import javax.swing.SwingUtilities

/**
 * UI冻结检测服务
 * 通过定期向EDT线程发送任务来检测UI是否冻结
 * 如果任务执行时间超过阈值，则认为发生了UI冻结
 */
@Service(Service.Level.PROJECT)
class UIFreezeDetectionService(private val project: Project) : Disposable, DumbAware {

    private val logger = LoggerManager.getLogger(javaClass)
    private val freezeLogger = UIFreezeLogger(project)
    private val settings = UIFreezeDetectionSettings.getInstance()

    // 服务状态
    private val isRunning = AtomicBoolean(false)
    private val serviceScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    // 检测状态
    private val lastResponseTime = AtomicLong(System.currentTimeMillis())
    private val pendingChecks = AtomicLong(0)

    /**
     * 启动UI冻结检测
     */
    fun startDetection() {
        if (!settings.enabled) {
            logger.info("UI冻结检测已禁用")
            return
        }

        if (isRunning.compareAndSet(false, true)) {
            logger.info("启动UI冻结检测服务")
            serviceScope.launch {
                runDetectionLoop()
            }
        } else {
            logger.debug("UI冻结检测服务已在运行")
        }
    }

    /**
     * 停止UI冻结检测
     */
    fun stopDetection() {
        if (isRunning.compareAndSet(true, false)) {
            logger.info("停止UI冻结检测服务")
        }
    }

    /**
     * 检测循环
     */
    private suspend fun runDetectionLoop() {
        while (isRunning.get() && !serviceScope.isActive.not() && settings.enabled) {
            try {
                performFreezeCheck()
                delay(settings.checkInterval)
            } catch (e: Exception) {
                if (e is CancellationException) {
                    logger.debug("UI冻结检测被取消")
                    break
                }
                logger.warn("UI冻结检测异常", e)
                delay(settings.checkInterval)
            }
        }
        logger.info("UI冻结检测循环结束")
    }

    /**
     * 执行冻结检查
     */
    private suspend fun performFreezeCheck() {
        val checkStartTime = System.currentTimeMillis()
        val checkId = pendingChecks.incrementAndGet()

        // 创建一个检查任务
        val checkTask = Runnable {
            lastResponseTime.set(System.currentTimeMillis())
            pendingChecks.decrementAndGet()
        }

        // 提交到EDT线程
        if (SwingUtilities.isEventDispatchThread()) {
            // 如果当前就在EDT线程，直接执行
            checkTask.run()
        } else {
            // 提交到EDT线程执行
            SwingUtilities.invokeLater(checkTask)
        }

        // 等待一段时间后检查是否完成
        delay(settings.freezeThreshold)

        val currentTime = System.currentTimeMillis()
        val responseDelay = currentTime - checkStartTime

        // 检查是否发生冻结
        if (pendingChecks.get() > 0 && responseDelay > settings.freezeThreshold) {
            val scenario = determineCurrentScenario()

            logger.warn("检测到UI冻结: 响应延迟=${responseDelay}ms, 场景=$scenario")

            // 记录冻结事件
            if (settings.logDetailedInfo) {
                freezeLogger.logUIFreeze(responseDelay, scenario)
            }

            // 如果是严重冻结，额外处理
            if (responseDelay > settings.severeThreshold) {
                handleSevereFreeze(responseDelay, scenario)
            }
        }
    }

    /**
     * 确定当前场景
     */
    private fun determineCurrentScenario(): String {
        return try {
            val scenarios = mutableListOf<String>()

            // 检查是否在索引过程中
            if (project.isDisposed.not()) {
                scenarios.add("项目活跃")
            }

            // 检查应用程序状态
            val app = ApplicationManager.getApplication()
            when {
                app.isWriteAccessAllowed -> scenarios.add("写操作")
                app.isReadAccessAllowed -> scenarios.add("读操作")
                app.isDispatchThread -> scenarios.add("EDT线程")
            }

            // 检查是否有模态对话框
            if (app.isActive) {
                scenarios.add("应用活跃")
            }

            if (scenarios.isEmpty()) {
                "未知场景"
            } else {
                scenarios.joinToString(", ")
            }
        } catch (e: Exception) {
            logger.debug("确定场景时发生异常", e)
            "场景检测异常: ${e.message}"
        }
    }

    /**
     * 处理严重冻结
     */
    private fun handleSevereFreeze(duration: Long, scenario: String) {
        logger.warn("检测到严重UI冻结: 持续时间=${duration}ms, 场景=$scenario")

        // 可以在这里添加额外的处理逻辑，比如：
        // 1. 发送通知给用户
        // 2. 收集更详细的诊断信息
        // 3. 尝试恢复操作

        serviceScope.launch {
            try {
                // 使用高级分析器分析线程转储
                if (settings.collectThreadDump) {
                    val analysisResult = ThreadDumpAnalyzer.analyzeCurrentThreadDump()

                    logger.warn("=== 严重UI冻结分析报告 ===")
                    logger.warn("摘要:\n${analysisResult.summary}")
                    logger.warn("EDT线程分析:\n${analysisResult.edtAnalysis}")

                    if (analysisResult.suspiciousThreads.isNotEmpty()) {
                        logger.warn("可疑线程:")
                        analysisResult.suspiciousThreads.forEach { thread ->
                            logger.warn("  - ${thread.name} (${thread.state})")
                            if (thread.suspiciousOperations.isNotEmpty()) {
                                logger.warn("    可疑操作: ${thread.suspiciousOperations.joinToString(", ")}")
                            }
                            if (thread.lockInfo != null) {
                                logger.warn("    锁信息: ${thread.lockInfo}")
                            }
                        }
                    }

                    if (analysisResult.possibleCauses.isNotEmpty()) {
                        logger.warn("可能原因:")
                        analysisResult.possibleCauses.forEach { cause ->
                            logger.warn("  - $cause")
                        }
                    }

                    if (analysisResult.recommendations.isNotEmpty()) {
                        logger.warn("建议:")
                        analysisResult.recommendations.forEach { recommendation ->
                            logger.warn("  - $recommendation")
                        }
                    }

                    // 如果需要，还可以记录详细的线程转储
                    val detailedDump = collectThreadDump()
                    logger.debug("详细线程转储:\n$detailedDump")
                }

                // 记录内存使用情况
                if (settings.collectMemoryInfo) {
                    val memoryInfo = collectMemoryInfo()
                    logger.warn("严重冻结时的内存信息:\n$memoryInfo")
                }

            } catch (e: Exception) {
                logger.warn("收集严重冻结诊断信息时发生异常", e)
            }
        }
    }

    /**
     * 收集线程转储信息
     */
    private fun collectThreadDump(): String {
        return try {
            val threadDump = StringBuilder()
            val analysis = analyzeThreadDump()

            // 添加分析结果
            threadDump.appendLine("=== 线程转储分析 ===")
            threadDump.appendLine(analysis)
            threadDump.appendLine()

            // 添加详细的线程信息
            threadDump.appendLine("=== 详细线程转储 ===")
            Thread.getAllStackTraces()
                .filter { (thread, _) -> isImportantThread(thread) }
                .forEach { (thread, stackTrace) ->
                    val priority = if (isImportantThread(thread)) "【重要】" else ""
                    threadDump.appendLine("线程: $priority${thread.name} (${thread.state}) - 优先级:${thread.priority}")

                    if (stackTrace.isNotEmpty()) {
                        stackTrace.forEach { element ->
                            threadDump.appendLine("  at $element")
                        }
                    } else {
                        threadDump.appendLine("  (无堆栈信息)")
                    }
                    threadDump.appendLine()
                }
            threadDump.toString()
        } catch (e: Exception) {
            "收集线程转储失败: ${e.message}"
        }
    }

    /**
     * 分析线程转储，找出可能导致UI冻结的原因
     */
    private fun analyzeThreadDump(): String {
        return try {
            val analysis = StringBuilder()
            val allThreads = Thread.getAllStackTraces()

            // 1. 分析EDT线程状态
            val edtThread = allThreads.entries.find {
                it.key.name.contains("AWT-EventQueue") || it.key.name.contains("EDT")
            }

            if (edtThread != null) {
                analysis.appendLine("【EDT线程分析】")
                analysis.appendLine("线程名: ${edtThread.key.name}")
                analysis.appendLine("状态: ${edtThread.key.state}")

                val suspiciousOperations = findSuspiciousOperations(edtThread.value)
                if (suspiciousOperations.isNotEmpty()) {
                    analysis.appendLine("可疑操作:")
                    suspiciousOperations.forEach { operation ->
                        analysis.appendLine("  - $operation")
                    }
                } else {
                    analysis.appendLine("未发现明显的阻塞操作")
                }
                analysis.appendLine()
            }

            // 2. 分析阻塞的线程
            val blockedThreads = allThreads.filter { it.key.state == Thread.State.BLOCKED }
            if (blockedThreads.isNotEmpty()) {
                analysis.appendLine("【阻塞线程分析】")
                analysis.appendLine("发现 ${blockedThreads.size} 个阻塞线程:")
                blockedThreads.forEach { (thread, stackTrace) ->
                    analysis.appendLine("  - ${thread.name} (${thread.state})")
                    val lockInfo = findLockInfo(stackTrace)
                    if (lockInfo.isNotEmpty()) {
                        analysis.appendLine("    等待锁: $lockInfo")
                    }
                }
                analysis.appendLine()
            }

            // 3. 分析等待的线程
            val waitingThreads = allThreads.filter {
                it.key.state == Thread.State.WAITING || it.key.state == Thread.State.TIMED_WAITING
            }
            if (waitingThreads.isNotEmpty()) {
                analysis.appendLine("【等待线程分析】")
                analysis.appendLine("发现 ${waitingThreads.size} 个等待线程")
                val importantWaitingThreads = waitingThreads.filter { isImportantThread(it.key) }
                if (importantWaitingThreads.isNotEmpty()) {
                    analysis.appendLine("重要的等待线程:")
                    importantWaitingThreads.forEach { (thread, _) ->
                        analysis.appendLine("  - ${thread.name} (${thread.state})")
                    }
                }
                analysis.appendLine()
            }

            // 4. 分析高CPU使用的线程（通过线程状态推测）
            val runningThreads = allThreads.filter { it.key.state == Thread.State.RUNNABLE }
            if (runningThreads.size > 10) {
                analysis.appendLine("【高活跃度分析】")
                analysis.appendLine("发现 ${runningThreads.size} 个运行中的线程，可能存在高CPU使用")
                analysis.appendLine()
            }

            // 5. 总结分析
            analysis.appendLine("【分析总结】")
            val possibleCauses = mutableListOf<String>()

            if (edtThread?.key?.state == Thread.State.BLOCKED) {
                possibleCauses.add("EDT线程被阻塞，可能在等待锁")
            }
            if (edtThread?.key?.state == Thread.State.WAITING || edtThread?.key?.state == Thread.State.TIMED_WAITING) {
                possibleCauses.add("EDT线程在等待，可能在等待其他线程完成")
            }
            if (blockedThreads.size > 5) {
                possibleCauses.add("大量线程阻塞，可能存在死锁或锁竞争")
            }
            if (runningThreads.size > 20) {
                possibleCauses.add("大量线程活跃，可能存在CPU密集型操作")
            }

            if (possibleCauses.isNotEmpty()) {
                analysis.appendLine("可能的冻结原因:")
                possibleCauses.forEach { cause ->
                    analysis.appendLine("  - $cause")
                }
            } else {
                analysis.appendLine("未能确定明确的冻结原因，可能是短暂的性能波动")
            }

            analysis.toString()
        } catch (e: Exception) {
            "线程转储分析失败: ${e.message}"
        }
    }

    /**
     * 判断是否是重要线程
     */
    private fun isImportantThread(thread: Thread): Boolean {
        val name = thread.name.lowercase()
        return name.contains("awt-eventqueue") ||
                name.contains("edt") ||
                name.contains("swing") ||
                name.contains("javafx") ||
                name.contains("idea") ||
                name.contains("intellij") ||
                name.contains("core") ||
                name.contains("aimi") ||
                name.contains("main")
    }

    /**
     * 在堆栈中查找可疑的操作
     */
    private fun findSuspiciousOperations(stackTrace: Array<StackTraceElement>): List<String> {
        val suspicious = mutableListOf<String>()

        stackTrace.forEach { element ->
            val className = element.className.lowercase()
            val methodName = element.methodName.lowercase()

            // 检查I/O操作
            if (className.contains("java.io") || className.contains("java.nio") ||
                methodName.contains("read") || methodName.contains("write")
            ) {
                suspicious.add("I/O操作: ${element.className}.${element.methodName}")
            }

            // 检查网络操作
            if (className.contains("java.net") || className.contains("http") ||
                className.contains("socket")
            ) {
                suspicious.add("网络操作: ${element.className}.${element.methodName}")
            }

            // 检查数据库操作
            if (className.contains("sql") || className.contains("jdbc") ||
                className.contains("database")
            ) {
                suspicious.add("数据库操作: ${element.className}.${element.methodName}")
            }

            // 检查同步操作
            if (methodName.contains("wait") || methodName.contains("lock") ||
                methodName.contains("synchronized")
            ) {
                suspicious.add("同步操作: ${element.className}.${element.methodName}")
            }

            // 检查文件系统操作
            if (className.contains("file") || className.contains("path") ||
                methodName.contains("exists") || methodName.contains("create")
            ) {
                suspicious.add("文件系统操作: ${element.className}.${element.methodName}")
            }

            // 检查反射操作
            if (className.contains("reflect") || methodName.contains("invoke")) {
                suspicious.add("反射操作: ${element.className}.${element.methodName}")
            }
        }

        return suspicious.distinct()
    }

    /**
     * 查找锁信息
     */
    private fun findLockInfo(stackTrace: Array<StackTraceElement>): String {
        stackTrace.forEach { element ->
            if (element.methodName.contains("lock") ||
                element.methodName.contains("wait") ||
                element.methodName.contains("synchronized")
            ) {
                return "${element.className}.${element.methodName}(${element.fileName}:${element.lineNumber})"
            }
        }
        return ""
    }

    /**
     * 收集内存信息
     */
    private fun collectMemoryInfo(): String {
        return try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val maxMemory = runtime.maxMemory()

            buildString {
                appendLine("内存使用情况:")
                appendLine("  已用内存: ${usedMemory / 1024 / 1024} MB")
                appendLine("  空闲内存: ${freeMemory / 1024 / 1024} MB")
                appendLine("  总内存: ${totalMemory / 1024 / 1024} MB")
                appendLine("  最大内存: ${maxMemory / 1024 / 1024} MB")
                appendLine("  内存使用率: ${String.format("%.1f", usedMemory.toDouble() / totalMemory * 100)}%")
            }
        } catch (e: Exception) {
            "收集内存信息失败: ${e.message}"
        }
    }

    /**
     * 获取检测统计信息
     */
    fun getDetectionStatistics(): String {
        return buildString {
            appendLine("=== UI冻结检测统计 ===")
            appendLine("检测状态: ${if (isRunning.get() && settings.enabled) "运行中" else "已停止"}")
            appendLine("检测间隔: ${settings.checkInterval}ms")
            appendLine("冻结阈值: ${settings.freezeThreshold}ms")
            appendLine("严重冻结阈值: ${settings.severeThreshold}ms")
            appendLine("详细日志: ${if (settings.logDetailedInfo) "启用" else "禁用"}")
            appendLine("线程转储: ${if (settings.collectThreadDump) "启用" else "禁用"}")
            appendLine("内存信息: ${if (settings.collectMemoryInfo) "启用" else "禁用"}")
            appendLine("待处理检查: ${pendingChecks.get()}")
            appendLine("最后响应时间: ${lastResponseTime.get()}")
            appendLine()
            append(freezeLogger.getRecentFreezeStatistics())
        }
    }

    override fun dispose() {
        stopDetection()
        serviceScope.cancel()
        logger.info("UI冻结检测服务已释放")
    }
}

package com.taobao.mc.aimi.psi

import com.intellij.lang.javascript.psi.*
import com.intellij.lang.javascript.psi.ecmal4.JSAttributeList
import com.intellij.lang.javascript.psi.ecmal4.JSAttributeListOwner
import com.intellij.lang.javascript.psi.ecmal4.JSClass
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiWhiteSpace
import com.intellij.psi.util.PsiTreeUtil
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.psi.types.*

/**
 * JavaScript/TypeScript 对象类型解析器
 * 专门处理 JavaScript/TypeScript 语言的类型解析和 Lambda 表达式
 */
class JavaScriptObjectTypeResolver : IObjectResolver {
    private val logger = LoggerManager.getLogger(javaClass)

    /**
     * 解析 JavaScript/TypeScript 对象
     */
    override fun resolveObjects(psiFile: PsiFile, element: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        // 首先检查是否在方法调用的括号内
        val methodCallReceiver = findMethodCallReceiver(element)
        if (methodCallReceiver != null) {
            logger.debug("Found TypeScript method call receiver: ${methodCallReceiver.text}")
            val receiverInfo = resolveTypeScriptExpressionType(methodCallReceiver as JSExpression)
            if (receiverInfo != null) {
                objects.add(receiverInfo.copy(confidence = 1.0, kind = ObjectKind.METHOD_CALL)) // 最高优先级
            }
        }

        // TypeScript/JavaScript的实现（简化版）
        val dotContext = findTypeScriptDotContext(psiFile, element)
        if (dotContext != null) {
            val identifier = dotContext.getCleanIdentifier()
            val objectInfo = resolveTypeScriptExpressionType(dotContext.expression)?.apply {
                if (identifier.isNotEmpty()) {
                    members = members.filter { it.name.contains(identifier, true) }
                }
            }
            if (objectInfo != null) {
                objects.add(objectInfo.copy(confidence = 1.0, kind = ObjectKind.DOT_CONTEXT))
            }
        } else {
            objects.addAll(findAvailableTypeScriptObjects(element))
        }

        return objects.sortedByDescending { it.confidence }
    }

    /**
     * 解析 JavaScript/TypeScript Lambda 参数
     */
    override fun resolveLambdaParameters(lambdaExpression: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        when (lambdaExpression) {
            /*is JSArrowFunction -> {
                // 箭头函数参数
                lambdaExpression.parameters.forEach { param ->
                    objects.add(
                        ObjectInfo(
                            name = param.name ?: "param",
                            type = param.typeElement?.text ?: "any",
                            qualifiedType = null,
                            kind = ObjectKind.LAMBDA_PARAMETER,
                            element = param,
                            members = emptyList(),
                            confidence = 1.0,
                            isLambdaParameter = true,
                            lambdaContext = "JavaScript Arrow Function"
                        )
                    )
                }
            }*/

            is JSFunctionExpression -> {
                // 函数表达式参数
                lambdaExpression.parameters.forEach { param ->
                    objects.add(
                        ObjectInfo(
                            name = param.name ?: "param",
                            type = param.typeElement?.text ?: "any",
                            qualifiedType = null,
                            kind = ObjectKind.LAMBDA_PARAMETER,
                            element = param,
                            members = emptyList(),
                            confidence = 1.0,
                            isLambdaParameter = true,
                            lambdaContext = "JavaScript Function Expression"
                        )
                    )
                }
            }
        }

        return objects
    }

    /**
     * 查找方法调用的接收者对象
     */
    private fun findMethodCallReceiver(element: PsiElement): PsiElement? {
        var current: PsiElement? = element

        // 向上查找，寻找方法调用表达式
        while (current != null) {
            when (current) {
                // TypeScript/JavaScript方法调用
                is JSCallExpression -> {
                    val methodExpression = current.methodExpression
                    if (methodExpression is JSReferenceExpression) {
                        return methodExpression.qualifier
                    }
                }
            }
            current = current.parent
        }

        return null
    }

    /**
     * 查找 TypeScript 点号上下文
     */
    private fun findTypeScriptDotContext(psiFile: PsiFile, element: PsiElement): DotRecord<JSExpression>? {
        val parent = element.parent
        if (parent is JSReferenceExpression && parent.qualifier != null) {
            return DotRecord(element.text, parent.qualifier!!)
        }
        if (element is PsiWhiteSpace) {
            return findTypeScriptDotContext(psiFile, element.prevSibling)
        }
        return null
    }

    /**
     * 解析TypeScript表达式类型
     */
    private fun resolveTypeScriptExpressionType(expression: JSExpression): ObjectInfo? {
        return when (expression) {
            is JSThisExpression -> {
                val containingClass = PsiTreeUtil.getParentOfType(expression, JSClass::class.java)
                ObjectInfo(
                    name = "this",
                    type = containingClass?.name ?: "Object",
                    qualifiedType = containingClass?.qualifiedName,
                    kind = ObjectKind.THIS,
                    element = containingClass,
                    members = extractTypeScriptClassMembers(containingClass),
                    confidence = 1.0
                )
            }

            is JSReferenceExpression -> {
                val resolved = expression.resolve()
                when (resolved) {
                    is JSVariable -> {
                        ObjectInfo(
                            name = resolved.name ?: "unknown",
                            type = resolved.typeElement?.text ?: "any",
                            qualifiedType = null,
                            kind = ObjectKind.LOCAL_VARIABLE,
                            element = resolved,
                            members = emptyList(), // 需要进一步实现
                            confidence = 0.9
                        )
                    }

                    else -> null
                }
            }

            else -> null
        }
    }

    /**
     * 查找可用的TypeScript对象
     */
    private fun findAvailableTypeScriptObjects(element: PsiElement): List<ObjectInfo> {
        val objects = mutableListOf<ObjectInfo>()

        // 添加this对象
        val containingClass = PsiTreeUtil.getParentOfType(element, JSClass::class.java)
        if (containingClass != null) {
            objects.add(
                ObjectInfo(
                    name = "this",
                    type = containingClass.name ?: "Object",
                    qualifiedType = containingClass.qualifiedName,
                    kind = ObjectKind.THIS,
                    element = containingClass,
                    members = extractTypeScriptClassMembers(containingClass),
                    confidence = 1.0
                )
            )
        }

        // 添加函数参数
        val containingFunction = PsiTreeUtil.getParentOfType(element, JSFunction::class.java)
        containingFunction?.parameters?.forEach { param ->
            objects.add(
                ObjectInfo(
                    name = param.name ?: "",
                    type = param.typeElement?.text ?: "any",
                    qualifiedType = null,
                    kind = ObjectKind.PARAMETER,
                    element = param,
                    members = emptyList(),
                    confidence = 0.9
                )
            )
        }

        return objects
    }

    /**
     * 提取TypeScript类的成员
     */
    private fun extractTypeScriptClassMembers(jsClass: JSClass?): List<MemberInfo> {
        if (jsClass == null) return emptyList()

        val members = mutableListOf<MemberInfo>()

        // 添加字段
        jsClass.fields.forEach { field ->
            members.add(
                MemberInfo(
                    name = field.name ?: "",
                    type = field.typeElement?.text ?: "any",
                    kind = MemberKind.FIELD,
                    visibility = getTypeScriptVisibility(field),
                    isStatic = field.hasModifier(JSAttributeList.ModifierType.STATIC)
                )
            )
        }

        // 添加方法
        jsClass.functions.forEach { function ->
            members.add(
                MemberInfo(
                    name = function.name ?: "",
                    type = function.returnTypeElement?.text ?: "any",
                    kind = MemberKind.METHOD,
                    visibility = getTypeScriptVisibility(function),
                    isStatic = function.hasModifier(JSAttributeList.ModifierType.STATIC),
                    signature = buildTypeScriptMethodSignature(function)
                )
            )
        }

        return members
    }

    /**
     * 获取TypeScript可见性
     */
    private fun getTypeScriptVisibility(member: JSAttributeListOwner): String {
        return when (member.attributeList?.accessType) {
            JSAttributeList.AccessType.PRIVATE -> "private"
            JSAttributeList.AccessType.PROTECTED -> "protected"
            else -> "public"
        }
    }

    /**
     * 构建TypeScript方法签名
     */
    private fun buildTypeScriptMethodSignature(function: JSFunction): String {
        val params = function.parameters.joinToString(", ") { param ->
            "${param.name}: ${param.typeElement?.text ?: "any"}"
        }
        return "${function.name}($params): ${function.returnTypeElement?.text ?: "any"}"
    }
}

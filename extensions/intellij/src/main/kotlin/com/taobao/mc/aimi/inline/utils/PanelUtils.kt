package com.taobao.mc.aimi.inline.utils

import com.intellij.collaboration.ui.jbColorFromHex
import com.intellij.ide.DataManager
import com.intellij.openapi.actionSystem.*
import com.intellij.openapi.actionSystem.impl.ActionButtonWithText
import com.intellij.openapi.actionSystem.impl.IdeaActionButtonLook
import com.intellij.openapi.actionSystem.impl.PresentationFactory
import com.intellij.openapi.keymap.KeymapManager
import com.intellij.openapi.keymap.KeymapUtil
import com.intellij.ui.Gray
import com.intellij.ui.JBColor
import com.intellij.util.ui.JBUI
import com.taobao.mc.aimi.util.ShortcutUtils
import org.apache.commons.lang3.StringUtils
import java.awt.*
import java.awt.event.ActionListener
import java.awt.geom.RoundRectangle2D
import javax.swing.*

/**
 * Utility class for creating UI panels and components
 */
object PanelUtils {

    private val DEFAULT_MINIMUM_BUTTON_SIZE: Dimension = JBUI.size(30, 20)
    private val presentationFactory = PresentationFactory()

    /**
     * Create a labeled component panel with optional dimensions
     */
    @JvmStatic
    fun createLabeledComponentPanel(labelText: String?, component: JComponent): JPanel {
        return createLabeledComponentPanel(labelText, component, null, null)
    }

    /**
     * Create a labeled component panel with specified dimensions
     */
    @JvmStatic
    fun createLabeledComponentPanel(
        labelText: String?,
        component: JComponent,
        minDimension: Dimension?,
        maxDimension: Dimension?
    ): JPanel {
        val panel = JPanel()
        panel.layout = BoxLayout(panel, BoxLayout.X_AXIS)

        maxDimension?.let { panel.maximumSize = it }

        if (StringUtils.isNotEmpty(labelText)) {
            val label = JLabel(labelText)
            panel.add(label)
        }

        panel.add(Box.createHorizontalStrut(10))

        val preferredSize = component.preferredSize
        component.minimumSize = minDimension ?: preferredSize
        component.maximumSize = maxDimension ?: preferredSize
        panel.add(component)

        panel.add(Box.createHorizontalGlue())

        return panel
    }

    /**
     * Create action and key set button panel
     */
    @JvmStatic
    fun createActionAndKeySetButton(actionIds: List<String>): JPanel {
        val panel = JPanel()
        panel.layout = FlowLayout(FlowLayout.LEFT, 4, 4)
        panel.isOpaque = false
        panel.maximumSize = Dimension(40, 20)

        val actionManager = ActionManager.getInstance()

        for (actionId in actionIds) {
            val action = actionManager.getAction(actionId) ?: continue

            var actionName = action.templatePresentation.text
            if (actionName.isNullOrEmpty()) {
                actionName = actionId
            }

            val shortcutText = getShortcutText(action)
            val button = createRoundedButton(actionName, shortcutText)
            panel.add(button)
        }

        return panel
    }

    /**
     * Get shortcut text for an action
     */
    private fun getShortcutText(action: AnAction): String {
        val shortcuts = action.shortcutSet.shortcuts
        return if (shortcuts.isNotEmpty() && shortcuts[0] is KeyboardShortcut) {
            KeymapUtil.getShortcutText(shortcuts[0])
        } else {
            ""
        }
    }

    /**
     * Create a rounded button with action and shortcut text
     */
    private fun createRoundedButton(actionText: String, shortcutText: String): JButton {
        val button = object : JButton("$actionText $shortcutText") {
            override fun paintComponent(g: Graphics) {
                val g2 = g.create() as Graphics2D
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

                val width = this.width
                val height = this.height

                g2.color = background
                val roundRect = RoundRectangle2D.Float(0f, 0f, width.toFloat(), height.toFloat(), 5f, 6f)
                g2.fill(roundRect)

                super.paintComponent(g2)
                g2.dispose()
            }
        }

        button.isOpaque = false
        button.isFocusPainted = false
        button.isBorderPainted = false
        button.isContentAreaFilled = false

        button.margin = JBUI.emptyInsets()
        button.font = button.font.deriveFont(12f)
        button.background = JBColor(Gray._220, Gray._75)
        button.foreground = JBColor(Gray._60, Gray._220)
        button.maximumSize = Dimension(button.preferredSize.width, 25)
        button.preferredSize = Dimension(button.preferredSize.width, 25)

        return button
    }

    /**
     * Create a number spinner with specified range and step
     */
    @JvmStatic
    fun createNumberSpinner(initialValue: Int, min: Int, max: Int, step: Int): JSpinner {
        val minValue = minOf(min, initialValue)
        val maxValue = maxOf(max, initialValue)
        val model = SpinnerNumberModel(initialValue, minValue, maxValue, step)
        return JSpinner(model)
    }

    /**
     * Create a text button with action
     */
    @JvmStatic
    fun createTextButton(actionId: String, actionText: String): ActionButtonWithText {
        val action = ActionManager.getInstance().getAction(actionId)
        val mnemonic = action.templatePresentation.mnemonic
        val presentation = presentationFactory.getPresentation(action)
        presentation.text = "$actionText(${ShortcutUtils.getActionInfo(actionId).second})"

        val buttonWithText = ActionButtonWithText(
            action,
            presentation,
            "EditorToolbar",
            DEFAULT_MINIMUM_BUTTON_SIZE
        )
        buttonWithText.setLook(AIMIActionButtonLook)


        val shortcutText = KeymapUtil.getShortcutText(actionId)
        val shortcuts = KeymapManager.getInstance().activeKeymap.getShortcuts(actionId)
        val shortcutSet = CustomShortcutSet(*shortcuts)
        KeymapUtil.getKeyStroke(shortcutSet)?.let { keyStroke ->
            val actionListener = ActionListener {
                buttonWithText.click()
            }
            // val keyStroke = KeyStroke.getKeyStroke(mnemonic, InputEvent.META_DOWN_MASK)
            buttonWithText.registerKeyboardAction(actionListener, keyStroke, JComponent.WHEN_IN_FOCUSED_WINDOW)
        }

        return buttonWithText
    }

    /**
     * Create an action button
     */
    @JvmStatic
    fun createActionButton(actionId: String): JComponent? {
        val action = ActionManager.getInstance().getAction(actionId)

        return if (action != null) {
            val presentation = action.templatePresentation.clone()
            val actionName = presentation.text
            val icon = presentation.icon

            val button = object : JButton(actionName) {
                private val action = action

                override fun addNotify() {
                    super.addNotify()
                    if (parent != null) {
                        action.registerCustomShortcutSet(action.shortcutSet, this)
                    }
                }
            }

            icon?.let { button.icon = it }

            button.preferredSize = JBUI.size(120, 32)

            button.addActionListener { e ->
                val dataContext = DataManager.getInstance().getDataContext(button)
                val event = AnActionEvent.createEvent(
                    action,
                    dataContext,
                    null,
                    "unknown",
                    ActionUiKind.NONE,
                    null,
                )
                action.actionPerformed(event)
            }

            button
        } else {
            null
        }
    }

    private object AIMIActionButtonLook : IdeaActionButtonLook() {
        val hoverColor: Color = jbColorFromHex("0x8B8DBF", "0x6264A7").brighter()

        override fun paintLookBackground(g: Graphics, rect: Rectangle, color: Color) {
            paintBackground(g, rect, hoverColor)
        }

        private fun paintBackground(g: Graphics, rect: Rectangle, color: Color) {
            val g2 = g.create() as Graphics2D

            try {
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)
                g2.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_NORMALIZE)
                g2.color = color

                val arc: Float = buttonArc.float
                g2.fill(RoundRectangle2D.Float(rect.x.toFloat(), rect.y.toFloat(), rect.width.toFloat(), rect.height.toFloat(), arc, arc))
            } finally {
                g2.dispose()
            }
        }
    }
}
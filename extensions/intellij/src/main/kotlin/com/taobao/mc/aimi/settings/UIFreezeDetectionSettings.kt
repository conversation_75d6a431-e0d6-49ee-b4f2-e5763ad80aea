package com.taobao.mc.aimi.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.*
import com.intellij.util.xmlb.XmlSerializerUtil

/**
 * UI冻结检测设置
 */
@Service
@State(
    name = "UIFreezeDetectionSettings",
    storages = [Storage("aimi-ui-freeze-detection.xml")]
)
class UIFreezeDetectionSettings : PersistentStateComponent<UIFreezeDetectionSettings.State> {

    data class State(
        var enabled: Boolean = true,
        var checkInterval: Long = 1000L, // 检测间隔（毫秒）
        var freezeThreshold: Long = 2000L, // 冻结阈值（毫秒）
        var severeThreshold: Long = 2000L, // 严重冻结阈值（毫秒）
        var logDetailedInfo: Boolean = true, // 是否记录详细信息
        var collectThreadDump: Boolean = true, // 是否收集线程转储
        var collectMemoryInfo: Boolean = true // 是否收集内存信息
    )

    private var state = State()

    override fun getState(): State = state

    override fun loadState(state: State) {
        XmlSerializerUtil.copyBean(state, this.state)
    }

    // 便捷访问方法
    var enabled: Boolean
        get() = state.enabled
        set(value) {
            state.enabled = value
        }

    var checkInterval: Long
        get() = state.checkInterval
        set(value) {
            state.checkInterval = value.coerceIn(500L, 10000L)
        }

    var freezeThreshold: Long
        get() = state.freezeThreshold
        set(value) {
            state.freezeThreshold = value.coerceIn(1000L, 30000L)
        }

    var severeThreshold: Long
        get() = state.severeThreshold
        set(value) {
            state.severeThreshold = value.coerceIn(2000L, 60000L)
        }

    var logDetailedInfo: Boolean
        get() = state.logDetailedInfo
        set(value) {
            state.logDetailedInfo = value
        }

    var collectThreadDump: Boolean
        get() = state.collectThreadDump
        set(value) {
            state.collectThreadDump = value
        }

    var collectMemoryInfo: Boolean
        get() = state.collectMemoryInfo
        set(value) {
            state.collectMemoryInfo = value
        }

    companion object {
        fun getInstance(): UIFreezeDetectionSettings {
            return ApplicationManager.getApplication().service<UIFreezeDetectionSettings>()
        }
    }
}

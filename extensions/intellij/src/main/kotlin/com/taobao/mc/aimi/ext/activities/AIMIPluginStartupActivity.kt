package com.taobao.mc.aimi.ext.activities

import com.intellij.ide.ui.LafManagerListener
import com.intellij.openapi.actionSystem.KeyboardShortcut
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ApplicationNamesInfo
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.keymap.KeymapManager
import com.intellij.openapi.module.ModuleManager
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.roots.ModuleRootManager
import com.intellij.openapi.startup.ProjectActivity
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.io.StreamUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.taobao.mc.aimi.ext.constants.getAIMIGlobalPath
import com.taobao.mc.aimi.ext.core.DiffManager
import com.taobao.mc.aimi.ext.core.GetTheme
import com.taobao.mc.aimi.ext.listeners.RemoveAllTooltipsListener
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.inline.hint.InlineHintSelectionChangedListener
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.util.childScope
import kotlinx.coroutines.launch
import java.io.File
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.nio.file.Paths
import javax.swing.KeyStroke

fun showTutorial(project: Project) {
    val tutorialFileName = getTutorialFileName()

    AIMIPluginStartupActivity::class.java.getClassLoader().getResourceAsStream(tutorialFileName)
        .use { `is` ->
            if (`is` == null) {
                throw IOException("Resource not found: $tutorialFileName")
            }
            var content = StreamUtil.readText(`is`, StandardCharsets.UTF_8)

            // All jetbrains will use J instead of L
            content = content.replace("[Cmd + L]", "[Cmd + J]")
            content = content.replace("[Cmd + Shift + L]", "[Cmd + Shift + J]")

            if (!System.getProperty("os.name").lowercase().contains("mac")) {
                content = content.replace("[Cmd + J]", "[Ctrl + J]")
                content = content.replace("[Cmd + Shift + J]", "[Ctrl + Shift + J]")
                content = content.replace("[Cmd + I]", "[Ctrl + I]")
                content = content.replace("⌘", "⌃")
            }
            val filepath = Paths.get(getAIMIGlobalPath(), tutorialFileName).toString()
            File(filepath).writeText(content)
            val virtualFile = LocalFileSystem.getInstance().findFileByPath(filepath)

            ApplicationManager.getApplication().invokeLater {
                if (virtualFile != null) {
                    FileEditorManager.getInstance(project).openFile(virtualFile, true)
                }
            }
        }
}

private fun getTutorialFileName(): String {
    val appName = ApplicationNamesInfo.getInstance().fullProductName.lowercase()
    return when {
        appName.contains("intellij") -> "continue_tutorial.java"
        appName.contains("pycharm") -> "continue_tutorial.py"
        appName.contains("webstorm") -> "continue_tutorial.ts"
        else -> "continue_tutorial.py" // Default to Python tutorial
    }
}

class AIMIPluginStartupActivity : ProjectActivity, DumbAware {

    override suspend fun execute(project: Project) {
        removeShortcutFromAction(getPlatformSpecificKeyStroke("J"))
        removeShortcutFromAction(getPlatformSpecificKeyStroke("shift J"))
        removeShortcutFromAction(getPlatformSpecificKeyStroke("I"))
        removeShortcutFromAction(getPlatformSpecificKeyStroke("BACK_SLASH"))
        initializePlugin(project)
    }

    private fun getPlatformSpecificKeyStroke(key: String): String {
        val osName = System.getProperty("os.name").lowercase()
        val modifier = if (osName.contains("mac")) "meta" else "control"
        return "$modifier $key"
    }

    private fun removeShortcutFromAction(shortcut: String) {
        val keymap = KeymapManager.getInstance().activeKeymap
        val keyStroke = KeyStroke.getKeyStroke(shortcut)
        val actionIds = keymap.getActionIds(keyStroke)

        // If AIMI has been re-assigned to another key, don't remove the shortcut
        if (!actionIds.any { it.startsWith("aimi") }) {
            return
        }

        for (actionId in actionIds) {
            if (actionId.startsWith("aimi")) {
                continue
            }
            val shortcuts = keymap.getShortcuts(actionId)
            for (shortcut in shortcuts) {
                if (shortcut is KeyboardShortcut && shortcut.firstKeyStroke == keyStroke) {
                    keymap.removeShortcut(actionId, shortcut)
                }
            }
        }
    }

    private fun initializePlugin(project: Project) {
        val continuePluginService = project.service<AIMIPluginService>()
        continuePluginService.initialize(project)
        val coroutineScope = continuePluginService.coroutineScope

        coroutineScope.launch {
            // val coreMessenger = continuePluginService.awaitCoreMessenger()
            val diffManager = DiffManager(project)
            continuePluginService.diffManager = diffManager

            // Listen to changes to settings so the ext can reload remote configuration
            val parentDisposable = AIMIPluginDisposable.getInstance(project)
            val connection = ApplicationManager.getApplication().messageBus.connect(
                parentDisposable
            )
            /*connection.subscribe(AIMISettingsListener.TOPIC, object : AIMISettingsListener {
                override fun settingsUpdated(settings: AIMISettingService.AIMIState) {
                    continuePluginService.coreMessenger?.request(
                        "config/ideSettingsUpdate", mapOf(
                            "remoteConfigServerUrl" to "",
                            "remoteConfigSyncPeriod" to 60,
                            "userToken" to "",
                        ), null
                    ) { _ -> }
                }
            })*/

            // Handle file changes and deletions - reindex
            // connection.subscribe(VirtualFileManager.VFS_CHANGES, object : BulkFileListener {
            //     override fun after(events: List<VFileEvent>) {
            //         val deletedURIs = events.filterIsInstance<VFileDeleteEvent>()
            //             .mapNotNull { event -> event.file.toUriOrNull() }
            //         if (deletedURIs.isNotEmpty()) {
            //             val data = mapOf("uris" to deletedURIs)
            //             coreMessenger.request("files/deleted", data, null) { _ -> }
            //         }
            //         val changedURIs = events.filterIsInstance<VFileContentChangeEvent>()
            //             .mapNotNull { event -> event.file.toUriOrNull() }
            //         if (changedURIs.isNotEmpty()) {
            //             val data = mapOf("uris" to changedURIs)
            //             coreMessenger.request("files/changed", data, null) { _ -> }
            //         }
            //         events.filterIsInstance<VFileCreateEvent>()
            //             .mapNotNull { event -> event.file?.toUriOrNull() }
            //             .takeIf { it.isNotEmpty() }?.let {
            //                 val data = mapOf("uris" to it)
            //                 coreMessenger.request("files/created", data, null) { _ -> }
            //             }
            //     }
            // })
            //
            // connection.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
            //     override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
            //         file.toUriOrNull()?.let { uri ->
            //             val data = mapOf("uris" to listOf(uri))
            //             coreMessenger.request("files/closed", data, null) { _ -> }
            //         }
            //     }
            //
            //     override fun fileOpened(source: FileEditorManager, file: VirtualFile) {
            //         file.toUriOrNull()?.let { uri ->
            //             val data = mapOf("uris" to listOf(uri))
            //             coreMessenger.request("files/opened", data, null) { _ -> }
            //         }
            //     }
            // })

            // Listen for theme changes
            connection.subscribe(LafManagerListener.TOPIC, LafManagerListener {
                val colors = GetTheme().getTheme();
                continuePluginService.sendToWebview(
                    MessageTypes.ToWebview.JetbrainsSetColors,
                    colors
                )
            })

            // Listen for clicking settings button to start the auth flow
            // val authService = service<AIMIAuthService>()
            // val initialSessionInfo = authService.loadControlPlaneSessionInfo()
            //
            // if (initialSessionInfo != null) {
            //     val data = mapOf(
            //         "sessionInfo" to initialSessionInfo
            //     )
            //     coreMessenger.request("didChangeControlPlaneSessionInfo", data, null) { _ -> }
            // }
            //
            // connection.subscribe(AuthListener.TOPIC, object : AuthListener {
            //     override fun startAuthFlow() {
            //         authService.startAuthFlow(project, false)
            //     }
            //
            //     override fun handleUpdatedSessionInfo(sessionInfo: ControlPlaneSessionInfo?) {
            //         val data = mapOf(
            //             "sessionInfo" to sessionInfo
            //         )
            //         coreMessenger.request(
            //             "didChangeControlPlaneSessionInfo",
            //             data,
            //             null
            //         ) { _ -> }
            //     }
            // })

            val listener = InlineHintSelectionChangedListener(coroutineScope.childScope("InlineHint"))
            connection.subscribe(RemoveAllTooltipsListener.TOPIC, listener)
            Disposer.register(parentDisposable, listener)

            // Reload the WebView
            continuePluginService.let { pluginService ->
                val allModulePaths = ModuleManager.getInstance(project).modules
                    .flatMap { module -> ModuleRootManager.getInstance(module).contentRoots.mapNotNull { it.toUriOrNull() } }

                val topLevelModulePaths = allModulePaths
                    .filter { modulePath -> allModulePaths.none { it != modulePath && modulePath.startsWith(it) } }

                pluginService.workspacePaths = topLevelModulePaths.toTypedArray()
            }

            EditorFactory.getInstance().eventMulticaster.addSelectionListener(
                listener,
                parentDisposable
            )

        }
    }
}
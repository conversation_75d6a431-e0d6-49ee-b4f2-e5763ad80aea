package com.taobao.mc.aimi.execution

/**
 * 终端执行结果
 * @param output 命令输出内容
 * @param isTerminated 进程是否已终止
 * @param exitCode 进程退出代码，如果进程未终止则为null
 */
data class TerminalResult(
    val output: String,
    val isTerminated: Boolean,
    val exitCode: Int?,
    val terminalId: Long,
) {
    /**
     * 判断命令是否成功执行（进程已终止且退出代码为0）
     */
    val isSuccess: Boolean
        get() = isTerminated && exitCode == 0
    
    /**
     * 获取简洁的状态描述
     */
    val statusDescription: String
        get() = when {
            !isTerminated -> "进程正在运行"
            exitCode == 0 -> "执行成功"
            else -> "执行失败，退出代码: $exitCode"
        }
}

package com.taobao.mc.aimi.ext.listeners

import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.SelectionModel
import com.intellij.openapi.editor.event.DocumentEvent
import com.intellij.openapi.editor.event.DocumentListener
import com.intellij.openapi.editor.event.SelectionEvent
import com.intellij.openapi.editor.event.SelectionListener
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.util.TextRange
import com.intellij.util.messages.Topic
import com.taobao.mc.aimi.editor.AIMIToolTipComponent
import com.taobao.mc.aimi.ext.utils.Debouncer
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.settings.AIMISettingService
import kotlinx.coroutines.CoroutineScope

interface RemoveAllTooltipsListener {
    companion object {
        @Topic.AppLevel
        val TOPIC: Topic<RemoveAllTooltipsListener> = Topic(RemoveAllTooltipsListener::class.java, Topic.BroadcastDirection.NONE, true)
    }

    fun removeAllTooltips()
}

class AIMIPluginSelectionListener(
    coroutineScope: CoroutineScope,
) : SelectionListener, RemoveAllTooltipsListener, DocumentListener, DumbAware, Disposable {

    private val logger = LoggerManager.getLogger(javaClass)
    private val debouncer = Debouncer(100, coroutineScope)
    private var toolTipComponents: ArrayList<AIMIToolTipComponent> = ArrayList()
    private var lastActiveEditor: Editor? = null

    override fun dispose() {
        removeAllTooltips()
        lastActiveEditor = null
    }

    override fun selectionChanged(e: SelectionEvent) {
        if (e.editor.isDisposed || e.editor.project?.isDisposed == true) {
            return
        }

        debouncer.debounce { handleSelection(e) }
    }

    override fun documentChanged(event: DocumentEvent) {
        removeAllTooltips()
    }

    override fun removeAllTooltips() {
        ApplicationManager.getApplication().invokeLater {
            toolTipComponents.forEach { tooltip ->
                tooltip.parent?.remove(tooltip)
            }
            toolTipComponents.clear()
        }
    }


    private fun handleSelection(e: SelectionEvent) {
        ApplicationManager.getApplication().invokeLater {
            val editor = e.editor

            if (!isFileEditor(editor)) {
                removeAllTooltips()
                return@invokeLater
            }

            // Fixes a bug where the tooltip isn't being disposed of when opening new files
            if (editor != lastActiveEditor) {
                removeAllTooltips()
                lastActiveEditor = editor
            }

            val model: SelectionModel = editor.selectionModel
            val selectedText = model.selectedText

            if (shouldRemoveTooltip(selectedText, editor)) {
                removeExistingTooltips(editor)
                return@invokeLater
            }

            updateTooltip(editor, model)
        }
    }

    private fun isFileEditor(editor: Editor): Boolean {
        val project = editor.project ?: return false
        val virtualFile = FileDocumentManager.getInstance().getFile(editor.document)

        // Check if the file exists and is not in-memory only
        if (virtualFile == null || !virtualFile.isInLocalFileSystem) {
            return false
        }

        // Check if the editor is not associated with a console
        val fileEditorManager = FileEditorManager.getInstance(project)
        val fileEditor = fileEditorManager.getSelectedEditor(virtualFile)

        return fileEditor is TextEditor
    }

    private fun shouldRemoveTooltip(selectedText: String?, editor: Editor): Boolean {
        return selectedText.isNullOrEmpty() ||
                !service<AIMISettingService>().state.displayEditorTooltip
    }

    private fun removeExistingTooltips(editor: Editor, onComplete: () -> Unit = {}) {
        ApplicationManager.getApplication().invokeLater {
            toolTipComponents.forEach {
                editor.contentComponent.remove(it)
            }
            editor.contentComponent.revalidate()
            editor.contentComponent.repaint()
            toolTipComponents.clear()
            onComplete()
        }
    }

    private fun updateTooltip(editor: Editor, model: SelectionModel) {
        removeExistingTooltips(editor) {
            ApplicationManager.getApplication().invokeLater {
                val document = editor.document
                val (startLine, endLine, isFullLineSelection) = getSelectionInfo(model, document)

                // Check if entire file is selected
                val isEntireFileSelected = model.selectionStart == 0 &&
                        model.selectionEnd == document.textLength

                // Scroll to top if entire file selected so that the user can see the input
                if (isEntireFileSelected) {
                    editor.scrollingModel.scrollTo(
                        LogicalPosition(0, 0),
                        com.intellij.openapi.editor.ScrollType.CENTER
                    )
                }

                // Get current cursor position
                val caretOffset = editor.caretModel.offset
                val tooltipPosition = calculateTooltipPosition(editor, model, document, caretOffset, startLine, endLine, isFullLineSelection)

                if (tooltipPosition != null) {
                    addToolTipComponent(editor, tooltipPosition.first, tooltipPosition.second)
                }
            }
        }
    }

    private fun getSelectionInfo(model: SelectionModel, document: Document): Triple<Int, Int, Boolean> {
        val startOffset = model.selectionStart
        val endOffset = model.selectionEnd
        val startLine = document.getLineNumber(startOffset)
        val endLine = document.getLineNumber(endOffset)
        val isFullLineSelection = startOffset == document.getLineStartOffset(startLine) &&
                ((endLine > 0 && endOffset == document.getLineEndOffset(endLine - 1)) || endOffset == document.getLineStartOffset(
                    endLine
                ))

        val adjustedEndLine = if (isFullLineSelection && endLine > startLine) endLine - 1 else endLine

        return Triple(startLine, adjustedEndLine, isFullLineSelection)
    }

    private fun calculateTooltipPosition(
        editor: Editor,
        model: SelectionModel,
        document: Document,
        caretOffset: Int,
        startLine: Int,
        endLine: Int,
        isFullLineSelection: Boolean
    ): Pair<Int, Int>? {
        val selectionStart = model.selectionStart
        val selectionEnd = model.selectionEnd

        // If no selection, return null
        if (selectionStart == selectionEnd) {
            return null
        }

        // Get cursor visual position
        val caretVisualPosition = editor.offsetToVisualPosition(caretOffset)
        val caretXY = editor.visualPositionToXY(caretVisualPosition)

        // Determine if cursor is at the start or end of selection
        val isCursorAtStart = caretOffset == selectionStart
        val isCursorAtEnd = caretOffset == selectionEnd

        // Calculate X position: cursor position + 16px offset
        val tooltipX = if (isCursorAtStart) {
            caretXY.x + 16
        } else {
            val lineStartOffset = getLineFirstNonWhitespaceOffset(document, endLine)
            val position = editor.offsetToVisualPosition(lineStartOffset)
            maxOf(editor.visualPositionToXY(position).x + 250, caretXY.x + 16)
        }

        val tooltipY = when {
            isCursorAtStart -> {
                // Cursor at start: show tooltip above selection
                val selectionStartLine = document.getLineNumber(selectionStart)
                val selectionTopY = editor.logicalPositionToXY(LogicalPosition(selectionStartLine, 0)).y
                selectionTopY - editor.lineHeight // Above the selection
            }

            else -> {
                // Cursor at end: show tooltip below selection
                val selectionEndLine = document.getLineNumber(selectionEnd)
                val selectionBottomY = editor.logicalPositionToXY(LogicalPosition(selectionEndLine, 0)).y
                selectionBottomY + editor.lineHeight * 2 // Below the selection
            }

            // else -> {
            //     // Cursor somewhere in the middle: show tooltip at cursor level
            //     caretXY.y
            // }
        }

        return Pair(tooltipX, tooltipY)
    }

    private fun addToolTipComponent(editor: Editor, tooltipX: Int, selectionTopY: Int) {
        val toolTipComponent = AIMIToolTipComponent(editor, tooltipX, selectionTopY)
        toolTipComponents.add(toolTipComponent)
        editor.contentComponent.add(toolTipComponent)
        editor.contentComponent.revalidate()
        editor.contentComponent.repaint()
    }

    fun getLineFirstNonWhitespaceOffset(document: Document, lineNumber: Int): Int {
        val lineStartOffset = document.getLineStartOffset(lineNumber)
        val lineEndOffset = document.getLineEndOffset(lineNumber)
        val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))

        // 找到第一个非空白字符的索引
        val firstNonWhitespaceIndex = lineText.indexOfFirst { !it.isWhitespace() }

        return if (firstNonWhitespaceIndex >= 0) {
            lineStartOffset + firstNonWhitespaceIndex
        } else {
            // 如果整行都是空白字符，返回
            lineStartOffset
        }
    }
}
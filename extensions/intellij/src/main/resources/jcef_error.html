<!--suppress ALL-->
<html>
<h2>
  <center>AIMI Chat 无法加载</center>
</h2>
<p>AIMI Chat 需要支持 Java Chromium Embedded Framework (JCEF) 的 JRE 来渲染我们的网页视图。您仍然可以使用自动补全和编辑功能。</p>
<h3>如何修复？</h3>
<p>您可以手动切换到支持 JCEF 的 JRE：</p>
<ol>
  <li>在主菜单中，转到 <b>帮助&nbsp;|&nbsp;查找操作</b> 或按
    <b>Ctrl&nbsp;+&nbsp;Shift&nbsp;+&nbsp;A</b>。
  </li>
  <li>查找并选择 <b>为 IDE 选择启动 Java 运行时</b> 操作。</li>
  <li>选择带有 JCEF 支持的运行时并点击 <b>确定</b>。</li>
  <li>使用新的运行时重新启动。</li>
</ol>
<p>支持 JCEF 的运行时通常称为 <b>JetBrains&nbsp;Runtime</b> 并带有 <code>-jcef</code> 后缀，
  例如 <code>21.0.3+13-509.11-jcef</code>。</p>
<br>
<small><a href="https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNRMGX56CZdL61boVrMqPxX6">JCEF设置指南</a></small>
</html>